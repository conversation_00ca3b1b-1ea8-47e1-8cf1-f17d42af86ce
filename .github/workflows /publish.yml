name: Publish to GitHub Pages

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.2"
          bundler-cache: true # This will cache dependencies

      - name: Update Bundler
        run: |
          gem update --system
          bundle update --bundler

      - name: Install dependencies
        run: |
          rm -f Gemfile.lock # Remove existing lockfile
          bundle install

      - name: Build site
        run: bundle exec jekyll build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./_site