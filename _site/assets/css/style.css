/*! normalize.css v4.1.1 | MIT License | github.com/necolas/normalize.css */
/** 1. Change the default font family in all browsers (opinionated). 2. Prevent adjustments of font size after orientation changes in IE and iOS. */
html { font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */ }

/** Remove the margin in all browsers (opinionated). */
body { margin: 0; }

/* HTML5 display definitions ========================================================================== */
/** Add the correct display in IE 9-. 1. Add the correct display in Edge, IE, and Firefox. 2. Add the correct display in IE. */
article, aside, details, figcaption, figure, footer, header, main, menu, nav, section { /* 1 */ display: block; }

summary { display: list-item; }

/** Add the correct display in IE 9-. */
audio, canvas, progress, video { display: inline-block; }

/** Add the correct display in iOS 4-7. */
audio:not([controls]) { display: none; height: 0; }

/** Add the correct vertical alignment in Chrome, Firefox, and Opera. */
progress { vertical-align: baseline; }

/** Add the correct display in IE 10-. 1. Add the correct display in IE. */
template, [hidden] { display: none !important; }

/* Links ========================================================================== */
/** Remove the gray background on active links in IE 10. */
a { background-color: transparent; /* 1 */ }

/** Remove the outline on focused links when they are also active or hovered in all browsers (opinionated). */
a:active, a:hover { outline-width: 0; }

/* Text-level semantics ========================================================================== */
/** 1. Remove the bottom border in Firefox 39-. 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari. */
abbr[title] { border-bottom: none; /* 1 */ text-decoration: underline; /* 2 */ text-decoration: underline dotted; /* 2 */ }

/** Prevent the duplicate application of `bolder` by the next rule in Safari 6. */
b, strong { font-weight: inherit; }

/** Add the correct font weight in Chrome, Edge, and Safari. */
b, strong { font-weight: bolder; }

/** Add the correct font style in Android 4.3-. */
dfn { font-style: italic; }

/** Correct the font size and margin on `h1` elements within `section` and `article` contexts in Chrome, Firefox, and Safari. */
h1 { font-size: 2em; margin: 0.67em 0; }

/** Add the correct background and color in IE 9-. */
mark { background-color: #ff0; color: #000; }

/** Add the correct font size in all browsers. */
small { font-size: 80%; }

/** Prevent `sub` and `sup` elements from affecting the line height in all browsers. */
sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }

sub { bottom: -0.25em; }

sup { top: -0.5em; }

/* Embedded content ========================================================================== */
/** Remove the border on images inside links in IE 10-. */
img { border-style: none; }

/** Hide the overflow in IE. */
svg:not(:root) { overflow: hidden; }

/* Grouping content ========================================================================== */
/** 1. Correct the inheritance and scaling of font size in all browsers. 2. Correct the odd `em` font sizing in all browsers. */
code, kbd, pre, samp { font-family: monospace, monospace; /* 1 */ font-size: 1em; /* 2 */ }

/** Add the correct margin in IE 8. */
figure { margin: 1em 40px; }

/** 1. Add the correct box sizing in Firefox. 2. Show the overflow in Edge and IE. */
hr { box-sizing: content-box; /* 1 */ height: 0; /* 1 */ overflow: visible; /* 2 */ }

/* Forms ========================================================================== */
/** 1. Change font properties to `inherit` in all browsers (opinionated). 2. Remove the margin in Firefox and Safari. */
button, input, select, textarea { font: inherit; /* 1 */ margin: 0; /* 2 */ }

/** Restore the font weight unset by the previous rule. */
optgroup { font-weight: bold; }

/** Show the overflow in IE. 1. Show the overflow in Edge. */
button, input { /* 1 */ overflow: visible; }

/** Remove the inheritance of text transform in Edge, Firefox, and IE. 1. Remove the inheritance of text transform in Firefox. */
button, select { /* 1 */ text-transform: none; }

/** 1. Prevent a WebKit bug where (2) destroys native `audio` and `video` controls in Android 4. 2. Correct the inability to style clickable types in iOS and Safari. */
button, html [type="button"], [type="reset"], [type="submit"] { -webkit-appearance: button; /* 2 */ }

/** Remove the inner border and padding in Firefox. */
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner { border-style: none; padding: 0; }

/** Restore the focus styles unset by the previous rule. */
button:-moz-focusring, [type="button"]:-moz-focusring, [type="reset"]:-moz-focusring, [type="submit"]:-moz-focusring { outline: 1px dotted ButtonText; }

/** Change the border, margin, and padding in all browsers (opinionated). */
fieldset { border: 1px solid #c0c0c0; margin: 0 2px; padding: 0.35em 0.625em 0.75em; }

/** 1. Correct the text wrapping in Edge and IE. 2. Correct the color inheritance from `fieldset` elements in IE. 3. Remove the padding so developers are not caught out when they zero out `fieldset` elements in all browsers. */
legend { box-sizing: border-box; /* 1 */ color: inherit; /* 2 */ display: table; /* 1 */ max-width: 100%; /* 1 */ padding: 0; /* 3 */ white-space: normal; /* 1 */ }

/** Remove the default vertical scrollbar in IE. */
textarea { overflow: auto; }

/** 1. Add the correct box sizing in IE 10-. 2. Remove the padding in IE 10-. */
[type="checkbox"], [type="radio"] { box-sizing: border-box; /* 1 */ padding: 0; /* 2 */ }

/** Correct the cursor style of increment and decrement buttons in Chrome. */
[type="number"]::-webkit-inner-spin-button, [type="number"]::-webkit-outer-spin-button { height: auto; }

/** 1. Correct the odd appearance in Chrome and Safari. 2. Correct the outline style in Safari. */
[type="search"] { -webkit-appearance: textfield; /* 1 */ outline-offset: -2px; /* 2 */ }

/** Remove the inner padding and cancel buttons in Chrome and Safari on OS X. */
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration { -webkit-appearance: none; }

/** Correct the text style of placeholders in Chrome, Edge, and Safari. */
::-webkit-input-placeholder { color: inherit; opacity: 0.54; }

/** 1. Correct the inability to style clickable types in iOS and Safari. 2. Change font properties to `inherit` in Safari. */
::-webkit-file-upload-button { -webkit-appearance: button; /* 1 */ font: inherit; /* 2 */ }

* { box-sizing: border-box; }

input, select, textarea, button { font-family: inherit; font-size: inherit; line-height: inherit; }

body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; font-size: 14px; line-height: 1.5; color: #24292e; background-color: #fff; }

a { color: #0366d6; text-decoration: none; }
a:hover { text-decoration: underline; }

b, strong { font-weight: 600; }

hr, .rule { height: 0; margin: 15px 0; overflow: hidden; background: transparent; border: 0; border-bottom: 1px solid #dfe2e5; }
hr::before, .rule::before { display: table; content: ""; }
hr::after, .rule::after { display: table; clear: both; content: ""; }

table { border-spacing: 0; border-collapse: collapse; }

td, th { padding: 0; }

button { cursor: pointer; border-radius: 0; }

[hidden][hidden] { display: none !important; }

details summary { cursor: pointer; }
details:not([open]) > *:not(summary) { display: none !important; }

h1, h2, h3, h4, h5, h6 { margin-top: 0; margin-bottom: 0; }

h1 { font-size: 32px; font-weight: 600; }

h2 { font-size: 24px; font-weight: 600; }

h3 { font-size: 20px; font-weight: 600; }

h4 { font-size: 16px; font-weight: 600; }

h5 { font-size: 14px; font-weight: 600; }

h6 { font-size: 12px; font-weight: 600; }

p { margin-top: 0; margin-bottom: 10px; }

small { font-size: 90%; }

blockquote { margin: 0; }

ul, ol { padding-left: 0; margin-top: 0; margin-bottom: 0; }

ol ol, ul ol { list-style-type: lower-roman; }

ul ul ol, ul ol ol, ol ul ol, ol ol ol { list-style-type: lower-alpha; }

dd { margin-left: 0; }

tt, code { font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace; font-size: 12px; }

pre { margin-top: 0; margin-bottom: 0; font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace; font-size: 12px; }

.octicon { vertical-align: text-bottom; }

/* Fade in an element */
.anim-fade-in { animation-name: fade-in; animation-duration: 1s; animation-timing-function: ease-in-out; }
.anim-fade-in.fast { animation-duration: 300ms; }

@keyframes fade-in { 0% { opacity: 0; }
  100% { opacity: 1; } }
/* Fade out an element */
.anim-fade-out { animation-name: fade-out; animation-duration: 1s; animation-timing-function: ease-out; }
.anim-fade-out.fast { animation-duration: 0.3s; }

@keyframes fade-out { 0% { opacity: 1; }
  100% { opacity: 0; } }
/* Fade in and slide up an element */
.anim-fade-up { opacity: 0; animation-name: fade-up; animation-duration: 0.3s; animation-fill-mode: forwards; animation-timing-function: ease-out; animation-delay: 1s; }

@keyframes fade-up { 0% { opacity: 0.8; transform: translateY(100%); }
  100% { opacity: 1; transform: translateY(0); } }
/* Fade an element out and slide down */
.anim-fade-down { animation-name: fade-down; animation-duration: 0.3s; animation-fill-mode: forwards; animation-timing-function: ease-in; }

@keyframes fade-down { 0% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0.5; transform: translateY(100%); } }
/* Grow an element width from 0 to 100% */
.anim-grow-x { width: 0%; animation-name: grow-x; animation-duration: 0.3s; animation-fill-mode: forwards; animation-timing-function: ease; animation-delay: 0.5s; }

@keyframes grow-x { to { width: 100%; } }
/* Shrink an element from 100% to 0% */
.anim-shrink-x { animation-name: shrink-x; animation-duration: 0.3s; animation-fill-mode: forwards; animation-timing-function: ease-in-out; animation-delay: 0.5s; }

@keyframes shrink-x { to { width: 0%; } }
/* Fade in an element and scale it fast */
.anim-scale-in { animation-name: scale-in; animation-duration: 0.15s; animation-timing-function: cubic-bezier(0.2, 0, 0.13, 1.5); }

@keyframes scale-in { 0% { opacity: 0; transform: scale(0.5); }
  100% { opacity: 1; transform: scale(1); } }
/* Pulse an element's opacity */
.anim-pulse { animation-name: pulse; animation-duration: 2s; animation-timing-function: linear; animation-iteration-count: infinite; }

@keyframes pulse { 0% { opacity: 0.3; }
  10% { opacity: 1; }
  100% { opacity: 0.3; } }
/* Pulse in an element */
.anim-pulse-in { animation-name: pulse-in; animation-duration: 0.5s; }

@keyframes pulse-in { 0% { transform: scale3d(1, 1, 1); }
  50% { transform: scale3d(1.1, 1.1, 1.1); }
  100% { transform: scale3d(1, 1, 1); } }
/* Increase scale of an element on hover */
.hover-grow { transition: transform 0.3s; backface-visibility: hidden; }
.hover-grow:hover { transform: scale(1.025); }

/* Add a gray border on all sides */
.border { border: 1px #e1e4e8 solid !important; }

/* Add a gray border to the left and right */
.border-y { border-top: 1px #e1e4e8 solid !important; border-bottom: 1px #e1e4e8 solid !important; }

/* Remove borders from all sides */
.border-0 { border: 0 !important; }

.border-dashed { border-style: dashed !important; }

/* Use with .border to turn the border blue */
.border-blue { border-color: #0366d6 !important; }

/* Use with .border to turn the border blue-light */
.border-blue-light { border-color: #c8e1ff !important; }

/* Use with .border to turn the border green */
.border-green { border-color: #34d058 !important; }

/* Use with .border to turn the border green light */
.border-green-light { border-color: #a2cbac !important; }

/* Use with .border to turn the border red */
.border-red { border-color: #d73a49 !important; }

/* Use with .border to turn the border red-light */
.border-red-light { border-color: #cea0a5 !important; }

/* Use with .border to turn the border purple */
.border-purple { border-color: #6f42c1 !important; }

/* Use with .border to turn the border yellow */
.border-yellow { border-color: #d9d0a5 !important; }

/* Use with .border to turn the border gray-light */
.border-gray-light { border-color: #eaecef !important; }

/* Use with .border to turn the border gray-dark */
.border-gray-dark { border-color: #d1d5da !important; }

/* Use with .border to turn the border rgba black 0.15 */
.border-black-fade { border-color: rgba(27, 31, 35, 0.15) !important; }

/* Add a gray border */
/* Add a gray border to the top */
.border-top { border-top: 1px #e1e4e8 solid !important; }

/* Add a gray border to the right */
.border-right { border-right: 1px #e1e4e8 solid !important; }

/* Add a gray border to the bottom */
.border-bottom { border-bottom: 1px #e1e4e8 solid !important; }

/* Add a gray border to the left */
.border-left { border-left: 1px #e1e4e8 solid !important; }

/* Remove the top border */
.border-top-0 { border-top: 0 !important; }

/* Remove the right border */
.border-right-0 { border-right: 0 !important; }

/* Remove the bottom border */
.border-bottom-0 { border-bottom: 0 !important; }

/* Remove the left border */
.border-left-0 { border-left: 0 !important; }

/* Remove the border-radius */
.rounded-0 { border-radius: 0 !important; }

/* Add a border-radius to all corners */
.rounded-1 { border-radius: 3px !important; }

/* Add a 2x border-radius to all corners */
.rounded-2 { border-radius: 6px !important; }

.rounded-top-0 { border-top-left-radius: 0 !important; border-top-right-radius: 0 !important; }

.rounded-top-1 { border-top-left-radius: 3px !important; border-top-right-radius: 3px !important; }

.rounded-top-2 { border-top-left-radius: 6px !important; border-top-right-radius: 6px !important; }

.rounded-right-0 { border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important; }

.rounded-right-1 { border-top-right-radius: 3px !important; border-bottom-right-radius: 3px !important; }

.rounded-right-2 { border-top-right-radius: 6px !important; border-bottom-right-radius: 6px !important; }

.rounded-bottom-0 { border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; }

.rounded-bottom-1 { border-bottom-right-radius: 3px !important; border-bottom-left-radius: 3px !important; }

.rounded-bottom-2 { border-bottom-right-radius: 6px !important; border-bottom-left-radius: 6px !important; }

.rounded-left-0 { border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important; }

.rounded-left-1 { border-bottom-left-radius: 3px !important; border-top-left-radius: 3px !important; }

.rounded-left-2 { border-bottom-left-radius: 6px !important; border-top-left-radius: 6px !important; }

@media (min-width: 544px) { /* Add a gray border */
  /* Add a gray border to the top */
  .border-sm-top { border-top: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the right */
  .border-sm-right { border-right: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the bottom */
  .border-sm-bottom { border-bottom: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the left */
  .border-sm-left { border-left: 1px #e1e4e8 solid !important; }
  /* Remove the top border */
  .border-sm-top-0 { border-top: 0 !important; }
  /* Remove the right border */
  .border-sm-right-0 { border-right: 0 !important; }
  /* Remove the bottom border */
  .border-sm-bottom-0 { border-bottom: 0 !important; }
  /* Remove the left border */
  .border-sm-left-0 { border-left: 0 !important; }
  /* Remove the border-radius */
  .rounded-sm-0 { border-radius: 0 !important; }
  /* Add a border-radius to all corners */
  .rounded-sm-1 { border-radius: 3px !important; }
  /* Add a 2x border-radius to all corners */
  .rounded-sm-2 { border-radius: 6px !important; }
  .rounded-sm-top-0 { border-top-left-radius: 0 !important; border-top-right-radius: 0 !important; }
  .rounded-sm-top-1 { border-top-left-radius: 3px !important; border-top-right-radius: 3px !important; }
  .rounded-sm-top-2 { border-top-left-radius: 6px !important; border-top-right-radius: 6px !important; }
  .rounded-sm-right-0 { border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important; }
  .rounded-sm-right-1 { border-top-right-radius: 3px !important; border-bottom-right-radius: 3px !important; }
  .rounded-sm-right-2 { border-top-right-radius: 6px !important; border-bottom-right-radius: 6px !important; }
  .rounded-sm-bottom-0 { border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; }
  .rounded-sm-bottom-1 { border-bottom-right-radius: 3px !important; border-bottom-left-radius: 3px !important; }
  .rounded-sm-bottom-2 { border-bottom-right-radius: 6px !important; border-bottom-left-radius: 6px !important; }
  .rounded-sm-left-0 { border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important; }
  .rounded-sm-left-1 { border-bottom-left-radius: 3px !important; border-top-left-radius: 3px !important; }
  .rounded-sm-left-2 { border-bottom-left-radius: 6px !important; border-top-left-radius: 6px !important; } }
@media (min-width: 768px) { /* Add a gray border */
  /* Add a gray border to the top */
  .border-md-top { border-top: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the right */
  .border-md-right { border-right: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the bottom */
  .border-md-bottom { border-bottom: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the left */
  .border-md-left { border-left: 1px #e1e4e8 solid !important; }
  /* Remove the top border */
  .border-md-top-0 { border-top: 0 !important; }
  /* Remove the right border */
  .border-md-right-0 { border-right: 0 !important; }
  /* Remove the bottom border */
  .border-md-bottom-0 { border-bottom: 0 !important; }
  /* Remove the left border */
  .border-md-left-0 { border-left: 0 !important; }
  /* Remove the border-radius */
  .rounded-md-0 { border-radius: 0 !important; }
  /* Add a border-radius to all corners */
  .rounded-md-1 { border-radius: 3px !important; }
  /* Add a 2x border-radius to all corners */
  .rounded-md-2 { border-radius: 6px !important; }
  .rounded-md-top-0 { border-top-left-radius: 0 !important; border-top-right-radius: 0 !important; }
  .rounded-md-top-1 { border-top-left-radius: 3px !important; border-top-right-radius: 3px !important; }
  .rounded-md-top-2 { border-top-left-radius: 6px !important; border-top-right-radius: 6px !important; }
  .rounded-md-right-0 { border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important; }
  .rounded-md-right-1 { border-top-right-radius: 3px !important; border-bottom-right-radius: 3px !important; }
  .rounded-md-right-2 { border-top-right-radius: 6px !important; border-bottom-right-radius: 6px !important; }
  .rounded-md-bottom-0 { border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; }
  .rounded-md-bottom-1 { border-bottom-right-radius: 3px !important; border-bottom-left-radius: 3px !important; }
  .rounded-md-bottom-2 { border-bottom-right-radius: 6px !important; border-bottom-left-radius: 6px !important; }
  .rounded-md-left-0 { border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important; }
  .rounded-md-left-1 { border-bottom-left-radius: 3px !important; border-top-left-radius: 3px !important; }
  .rounded-md-left-2 { border-bottom-left-radius: 6px !important; border-top-left-radius: 6px !important; } }
@media (min-width: 1012px) { /* Add a gray border */
  /* Add a gray border to the top */
  .border-lg-top { border-top: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the right */
  .border-lg-right { border-right: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the bottom */
  .border-lg-bottom { border-bottom: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the left */
  .border-lg-left { border-left: 1px #e1e4e8 solid !important; }
  /* Remove the top border */
  .border-lg-top-0 { border-top: 0 !important; }
  /* Remove the right border */
  .border-lg-right-0 { border-right: 0 !important; }
  /* Remove the bottom border */
  .border-lg-bottom-0 { border-bottom: 0 !important; }
  /* Remove the left border */
  .border-lg-left-0 { border-left: 0 !important; }
  /* Remove the border-radius */
  .rounded-lg-0 { border-radius: 0 !important; }
  /* Add a border-radius to all corners */
  .rounded-lg-1 { border-radius: 3px !important; }
  /* Add a 2x border-radius to all corners */
  .rounded-lg-2 { border-radius: 6px !important; }
  .rounded-lg-top-0 { border-top-left-radius: 0 !important; border-top-right-radius: 0 !important; }
  .rounded-lg-top-1 { border-top-left-radius: 3px !important; border-top-right-radius: 3px !important; }
  .rounded-lg-top-2 { border-top-left-radius: 6px !important; border-top-right-radius: 6px !important; }
  .rounded-lg-right-0 { border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important; }
  .rounded-lg-right-1 { border-top-right-radius: 3px !important; border-bottom-right-radius: 3px !important; }
  .rounded-lg-right-2 { border-top-right-radius: 6px !important; border-bottom-right-radius: 6px !important; }
  .rounded-lg-bottom-0 { border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; }
  .rounded-lg-bottom-1 { border-bottom-right-radius: 3px !important; border-bottom-left-radius: 3px !important; }
  .rounded-lg-bottom-2 { border-bottom-right-radius: 6px !important; border-bottom-left-radius: 6px !important; }
  .rounded-lg-left-0 { border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important; }
  .rounded-lg-left-1 { border-bottom-left-radius: 3px !important; border-top-left-radius: 3px !important; }
  .rounded-lg-left-2 { border-bottom-left-radius: 6px !important; border-top-left-radius: 6px !important; } }
@media (min-width: 1280px) { /* Add a gray border */
  /* Add a gray border to the top */
  .border-xl-top { border-top: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the right */
  .border-xl-right { border-right: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the bottom */
  .border-xl-bottom { border-bottom: 1px #e1e4e8 solid !important; }
  /* Add a gray border to the left */
  .border-xl-left { border-left: 1px #e1e4e8 solid !important; }
  /* Remove the top border */
  .border-xl-top-0 { border-top: 0 !important; }
  /* Remove the right border */
  .border-xl-right-0 { border-right: 0 !important; }
  /* Remove the bottom border */
  .border-xl-bottom-0 { border-bottom: 0 !important; }
  /* Remove the left border */
  .border-xl-left-0 { border-left: 0 !important; }
  /* Remove the border-radius */
  .rounded-xl-0 { border-radius: 0 !important; }
  /* Add a border-radius to all corners */
  .rounded-xl-1 { border-radius: 3px !important; }
  /* Add a 2x border-radius to all corners */
  .rounded-xl-2 { border-radius: 6px !important; }
  .rounded-xl-top-0 { border-top-left-radius: 0 !important; border-top-right-radius: 0 !important; }
  .rounded-xl-top-1 { border-top-left-radius: 3px !important; border-top-right-radius: 3px !important; }
  .rounded-xl-top-2 { border-top-left-radius: 6px !important; border-top-right-radius: 6px !important; }
  .rounded-xl-right-0 { border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important; }
  .rounded-xl-right-1 { border-top-right-radius: 3px !important; border-bottom-right-radius: 3px !important; }
  .rounded-xl-right-2 { border-top-right-radius: 6px !important; border-bottom-right-radius: 6px !important; }
  .rounded-xl-bottom-0 { border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; }
  .rounded-xl-bottom-1 { border-bottom-right-radius: 3px !important; border-bottom-left-radius: 3px !important; }
  .rounded-xl-bottom-2 { border-bottom-right-radius: 6px !important; border-bottom-left-radius: 6px !important; }
  .rounded-xl-left-0 { border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important; }
  .rounded-xl-left-1 { border-bottom-left-radius: 3px !important; border-top-left-radius: 3px !important; }
  .rounded-xl-left-2 { border-bottom-left-radius: 6px !important; border-top-left-radius: 6px !important; } }
/* Add a 50% border-radius to make something into a circle */
.circle { border-radius: 50% !important; }

.box-shadow { box-shadow: 0 1px 1px rgba(27, 31, 35, 0.1) !important; }

.box-shadow-medium { box-shadow: 0 1px 5px rgba(27, 31, 35, 0.15) !important; }

.box-shadow-large { box-shadow: 0 1px 15px rgba(27, 31, 35, 0.15) !important; }

.box-shadow-extra-large { box-shadow: 0 10px 50px rgba(27, 31, 35, 0.07) !important; }

.box-shadow-none { box-shadow: none !important; }

/* Set the background to $bg-white */
.bg-white { background-color: #fff !important; }

/* Set the background to $bg-blue */
.bg-blue { background-color: #0366d6 !important; }

/* Set the background to $bg-blue-light */
.bg-blue-light { background-color: #f1f8ff !important; }

/* Set the background to $bg-gray-dark */
.bg-gray-dark { background-color: #24292e !important; }

/* Set the background to $bg-gray */
.bg-gray { background-color: #f6f8fa !important; }

/* Set the background to $bg-gray-light */
.bg-gray-light { background-color: #fafbfc !important; }

/* Set the background to $bg-green */
.bg-green { background-color: #28a745 !important; }

/* Set the background to $bg-green-light */
.bg-green-light { background-color: #dcffe4 !important; }

/* Set the background to $bg-red */
.bg-red { background-color: #d73a49 !important; }

/* Set the background to $bg-red-light */
.bg-red-light { background-color: #ffdce0 !important; }

/* Set the background to $bg-yellow */
.bg-yellow { background-color: #ffd33d !important; }

/* Set the background to $bg-yellow-light */
.bg-yellow-light { background-color: #fff5b1 !important; }

/* Set the background to $bg-purple */
.bg-purple { background-color: #6f42c1 !important; }

/* Set the background to $bg-purple-light */
.bg-purple-light { background-color: #f5f0ff !important; }

.bg-shade-gradient { background-image: linear-gradient(180deg, rgba(27, 31, 35, 0.065), rgba(27, 31, 35, 0)) !important; background-repeat: no-repeat !important; background-size: 100% 200px !important; }

/* Set the text color to $text-blue */
.text-blue { color: #0366d6 !important; }

/* Set the text color to $text-red */
.text-red { color: #cb2431 !important; }

/* Set the text color to $text-gray-light */
.text-gray-light { color: #6a737d !important; }

/* Set the text color to $text-gray */
.text-gray { color: #586069 !important; }

/* Set the text color to $text-gray-dark */
.text-gray-dark { color: #24292e !important; }

/* Set the text color to $text-green */
.text-green { color: #28a745 !important; }

/* Set the text color to $text-orange */
.text-orange { color: #a04100 !important; }

/* Set the text color to $text-orange-light */
.text-orange-light { color: #e36209 !important; }

/* Set the text color to $text-purple */
.text-purple { color: #6f42c1 !important; }

/* Set the text color to $text-white */
.text-white { color: #fff !important; }

/* Set the text color to inherit */
.text-inherit { color: inherit !important; }

.text-pending { color: #b08800 !important; }

.bg-pending { color: #dbab09 !important; }

.link-gray { color: #586069 !important; }
.link-gray:hover { color: #0366d6 !important; }

.link-gray-dark { color: #24292e !important; }
.link-gray-dark:hover { color: #0366d6 !important; }

/* Set the link color to $text-blue on hover Useful when you want only part of a link to turn blue on hover */
.link-hover-blue:hover { color: #0366d6 !important; }

/* Make a link $text-gray, then $text-blue on hover and removes the underline */
.muted-link { color: #586069 !important; }
.muted-link:hover { color: #0366d6 !important; text-decoration: none; }

.details-overlay[open] > summary::before { position: fixed; top: 0; right: 0; bottom: 0; left: 0; z-index: 80; display: block; cursor: default; content: " "; background: transparent; }

.details-overlay-dark[open] > summary::before { z-index: 99; background: rgba(27, 31, 35, 0.5); }

.flex-row { flex-direction: row !important; }

.flex-row-reverse { flex-direction: row-reverse !important; }

.flex-column { flex-direction: column !important; }

.flex-wrap { flex-wrap: wrap !important; }

.flex-nowrap { flex-wrap: nowrap !important; }

.flex-justify-start { justify-content: flex-start !important; }

.flex-justify-end { justify-content: flex-end !important; }

.flex-justify-center { justify-content: center !important; }

.flex-justify-between { justify-content: space-between !important; }

.flex-justify-around { justify-content: space-around !important; }

.flex-items-start { align-items: flex-start !important; }

.flex-items-end { align-items: flex-end !important; }

.flex-items-center { align-items: center !important; }

.flex-items-baseline { align-items: baseline !important; }

.flex-items-stretch { align-items: stretch !important; }

.flex-content-start { align-content: flex-start !important; }

.flex-content-end { align-content: flex-end !important; }

.flex-content-center { align-content: center !important; }

.flex-content-between { align-content: space-between !important; }

.flex-content-around { align-content: space-around !important; }

.flex-content-stretch { align-content: stretch !important; }

.flex-auto { flex: 1 1 auto !important; }

.flex-shrink-0 { flex-shrink: 0 !important; }

.flex-self-auto { align-self: auto !important; }

.flex-self-start { align-self: flex-start !important; }

.flex-self-end { align-self: flex-end !important; }

.flex-self-center { align-self: center !important; }

.flex-self-baseline { align-self: baseline !important; }

.flex-self-stretch { align-self: stretch !important; }

.flex-item-equal { flex-grow: 1; flex-basis: 0; }

@media (min-width: 544px) { .flex-sm-row { flex-direction: row !important; }
  .flex-sm-row-reverse { flex-direction: row-reverse !important; }
  .flex-sm-column { flex-direction: column !important; }
  .flex-sm-wrap { flex-wrap: wrap !important; }
  .flex-sm-nowrap { flex-wrap: nowrap !important; }
  .flex-sm-justify-start { justify-content: flex-start !important; }
  .flex-sm-justify-end { justify-content: flex-end !important; }
  .flex-sm-justify-center { justify-content: center !important; }
  .flex-sm-justify-between { justify-content: space-between !important; }
  .flex-sm-justify-around { justify-content: space-around !important; }
  .flex-sm-items-start { align-items: flex-start !important; }
  .flex-sm-items-end { align-items: flex-end !important; }
  .flex-sm-items-center { align-items: center !important; }
  .flex-sm-items-baseline { align-items: baseline !important; }
  .flex-sm-items-stretch { align-items: stretch !important; }
  .flex-sm-content-start { align-content: flex-start !important; }
  .flex-sm-content-end { align-content: flex-end !important; }
  .flex-sm-content-center { align-content: center !important; }
  .flex-sm-content-between { align-content: space-between !important; }
  .flex-sm-content-around { align-content: space-around !important; }
  .flex-sm-content-stretch { align-content: stretch !important; }
  .flex-sm-auto { flex: 1 1 auto !important; }
  .flex-sm-shrink-0 { flex-shrink: 0 !important; }
  .flex-sm-self-auto { align-self: auto !important; }
  .flex-sm-self-start { align-self: flex-start !important; }
  .flex-sm-self-end { align-self: flex-end !important; }
  .flex-sm-self-center { align-self: center !important; }
  .flex-sm-self-baseline { align-self: baseline !important; }
  .flex-sm-self-stretch { align-self: stretch !important; }
  .flex-sm-item-equal { flex-grow: 1; flex-basis: 0; } }
@media (min-width: 768px) { .flex-md-row { flex-direction: row !important; }
  .flex-md-row-reverse { flex-direction: row-reverse !important; }
  .flex-md-column { flex-direction: column !important; }
  .flex-md-wrap { flex-wrap: wrap !important; }
  .flex-md-nowrap { flex-wrap: nowrap !important; }
  .flex-md-justify-start { justify-content: flex-start !important; }
  .flex-md-justify-end { justify-content: flex-end !important; }
  .flex-md-justify-center { justify-content: center !important; }
  .flex-md-justify-between { justify-content: space-between !important; }
  .flex-md-justify-around { justify-content: space-around !important; }
  .flex-md-items-start { align-items: flex-start !important; }
  .flex-md-items-end { align-items: flex-end !important; }
  .flex-md-items-center { align-items: center !important; }
  .flex-md-items-baseline { align-items: baseline !important; }
  .flex-md-items-stretch { align-items: stretch !important; }
  .flex-md-content-start { align-content: flex-start !important; }
  .flex-md-content-end { align-content: flex-end !important; }
  .flex-md-content-center { align-content: center !important; }
  .flex-md-content-between { align-content: space-between !important; }
  .flex-md-content-around { align-content: space-around !important; }
  .flex-md-content-stretch { align-content: stretch !important; }
  .flex-md-auto { flex: 1 1 auto !important; }
  .flex-md-shrink-0 { flex-shrink: 0 !important; }
  .flex-md-self-auto { align-self: auto !important; }
  .flex-md-self-start { align-self: flex-start !important; }
  .flex-md-self-end { align-self: flex-end !important; }
  .flex-md-self-center { align-self: center !important; }
  .flex-md-self-baseline { align-self: baseline !important; }
  .flex-md-self-stretch { align-self: stretch !important; }
  .flex-md-item-equal { flex-grow: 1; flex-basis: 0; } }
@media (min-width: 1012px) { .flex-lg-row { flex-direction: row !important; }
  .flex-lg-row-reverse { flex-direction: row-reverse !important; }
  .flex-lg-column { flex-direction: column !important; }
  .flex-lg-wrap { flex-wrap: wrap !important; }
  .flex-lg-nowrap { flex-wrap: nowrap !important; }
  .flex-lg-justify-start { justify-content: flex-start !important; }
  .flex-lg-justify-end { justify-content: flex-end !important; }
  .flex-lg-justify-center { justify-content: center !important; }
  .flex-lg-justify-between { justify-content: space-between !important; }
  .flex-lg-justify-around { justify-content: space-around !important; }
  .flex-lg-items-start { align-items: flex-start !important; }
  .flex-lg-items-end { align-items: flex-end !important; }
  .flex-lg-items-center { align-items: center !important; }
  .flex-lg-items-baseline { align-items: baseline !important; }
  .flex-lg-items-stretch { align-items: stretch !important; }
  .flex-lg-content-start { align-content: flex-start !important; }
  .flex-lg-content-end { align-content: flex-end !important; }
  .flex-lg-content-center { align-content: center !important; }
  .flex-lg-content-between { align-content: space-between !important; }
  .flex-lg-content-around { align-content: space-around !important; }
  .flex-lg-content-stretch { align-content: stretch !important; }
  .flex-lg-auto { flex: 1 1 auto !important; }
  .flex-lg-shrink-0 { flex-shrink: 0 !important; }
  .flex-lg-self-auto { align-self: auto !important; }
  .flex-lg-self-start { align-self: flex-start !important; }
  .flex-lg-self-end { align-self: flex-end !important; }
  .flex-lg-self-center { align-self: center !important; }
  .flex-lg-self-baseline { align-self: baseline !important; }
  .flex-lg-self-stretch { align-self: stretch !important; }
  .flex-lg-item-equal { flex-grow: 1; flex-basis: 0; } }
@media (min-width: 1280px) { .flex-xl-row { flex-direction: row !important; }
  .flex-xl-row-reverse { flex-direction: row-reverse !important; }
  .flex-xl-column { flex-direction: column !important; }
  .flex-xl-wrap { flex-wrap: wrap !important; }
  .flex-xl-nowrap { flex-wrap: nowrap !important; }
  .flex-xl-justify-start { justify-content: flex-start !important; }
  .flex-xl-justify-end { justify-content: flex-end !important; }
  .flex-xl-justify-center { justify-content: center !important; }
  .flex-xl-justify-between { justify-content: space-between !important; }
  .flex-xl-justify-around { justify-content: space-around !important; }
  .flex-xl-items-start { align-items: flex-start !important; }
  .flex-xl-items-end { align-items: flex-end !important; }
  .flex-xl-items-center { align-items: center !important; }
  .flex-xl-items-baseline { align-items: baseline !important; }
  .flex-xl-items-stretch { align-items: stretch !important; }
  .flex-xl-content-start { align-content: flex-start !important; }
  .flex-xl-content-end { align-content: flex-end !important; }
  .flex-xl-content-center { align-content: center !important; }
  .flex-xl-content-between { align-content: space-between !important; }
  .flex-xl-content-around { align-content: space-around !important; }
  .flex-xl-content-stretch { align-content: stretch !important; }
  .flex-xl-auto { flex: 1 1 auto !important; }
  .flex-xl-shrink-0 { flex-shrink: 0 !important; }
  .flex-xl-self-auto { align-self: auto !important; }
  .flex-xl-self-start { align-self: flex-start !important; }
  .flex-xl-self-end { align-self: flex-end !important; }
  .flex-xl-self-center { align-self: center !important; }
  .flex-xl-self-baseline { align-self: baseline !important; }
  .flex-xl-self-stretch { align-self: stretch !important; }
  .flex-xl-item-equal { flex-grow: 1; flex-basis: 0; } }
/* Set position to static */
.position-static { position: static !important; }

/* Set position to relative */
.position-relative { position: relative !important; }

/* Set position to absolute */
.position-absolute { position: absolute !important; }

/* Set position to fixed */
.position-fixed { position: fixed !important; }

/* Set top 0 */
.top-0 { top: 0 !important; }

/* Set right 0 */
.right-0 { right: 0 !important; }

/* Set bottom 0 */
.bottom-0 { bottom: 0 !important; }

/* Set left 0 */
.left-0 { left: 0 !important; }

/* Vertical align middle */
.v-align-middle { vertical-align: middle !important; }

/* Vertical align top */
.v-align-top { vertical-align: top !important; }

/* Vertical align bottom */
.v-align-bottom { vertical-align: bottom !important; }

/* Vertical align to the top of the text */
.v-align-text-top { vertical-align: text-top !important; }

/* Vertical align to the bottom of the text */
.v-align-text-bottom { vertical-align: text-bottom !important; }

/* Vertical align to the parent's baseline */
.v-align-baseline { vertical-align: baseline !important; }

/* Set the overflow hidden */
.overflow-hidden { overflow: hidden !important; }

/* Set the overflow scroll */
.overflow-scroll { overflow: scroll !important; }

/* Set the overflow auto */
.overflow-auto { overflow: auto !important; }

/* Clear floats around the element */
.clearfix::before { display: table; content: ""; }
.clearfix::after { display: table; clear: both; content: ""; }

/* Float to the left */
.float-left { float: left !important; }

/* Float to the right */
.float-right { float: right !important; }

/* No float */
.float-none { float: none !important; }

@media (min-width: 544px) { /* Float to the left */
  .float-sm-left { float: left !important; }
  /* Float to the right */
  .float-sm-right { float: right !important; }
  /* No float */
  .float-sm-none { float: none !important; } }
@media (min-width: 768px) { /* Float to the left */
  .float-md-left { float: left !important; }
  /* Float to the right */
  .float-md-right { float: right !important; }
  /* No float */
  .float-md-none { float: none !important; } }
@media (min-width: 1012px) { /* Float to the left */
  .float-lg-left { float: left !important; }
  /* Float to the right */
  .float-lg-right { float: right !important; }
  /* No float */
  .float-lg-none { float: none !important; } }
@media (min-width: 1280px) { /* Float to the left */
  .float-xl-left { float: left !important; }
  /* Float to the right */
  .float-xl-right { float: right !important; }
  /* No float */
  .float-xl-none { float: none !important; } }
/* Max width 100% */
.width-fit { max-width: 100% !important; }

/* Set the width to 100% */
.width-full { width: 100% !important; }

/* Max height 100% */
.height-fit { max-height: 100% !important; }

/* Set the height to 100% */
.height-full { height: 100% !important; }

/* Remove min-width from element */
.min-width-0 { min-width: 0 !important; }

/* Set the direction to rtl */
.direction-rtl { direction: rtl !important; }

/* Set the direction to ltr */
.direction-ltr { direction: ltr !important; }

@media (min-width: 544px) { /* Set the direction to rtl */
  .direction-sm-rtl { direction: rtl !important; }
  /* Set the direction to ltr */
  .direction-sm-ltr { direction: ltr !important; } }
@media (min-width: 768px) { /* Set the direction to rtl */
  .direction-md-rtl { direction: rtl !important; }
  /* Set the direction to ltr */
  .direction-md-ltr { direction: ltr !important; } }
@media (min-width: 1012px) { /* Set the direction to rtl */
  .direction-lg-rtl { direction: rtl !important; }
  /* Set the direction to ltr */
  .direction-lg-ltr { direction: ltr !important; } }
@media (min-width: 1280px) { /* Set the direction to rtl */
  .direction-xl-rtl { direction: rtl !important; }
  /* Set the direction to ltr */
  .direction-xl-ltr { direction: ltr !important; } }
/* Set a $size margin to all sides at $breakpoint */
.m-0 { margin: 0 !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-0 { margin-top: 0 !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-0 { margin-right: 0 !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-0 { margin-bottom: 0 !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-0 { margin-left: 0 !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-0 { margin-right: 0 !important; margin-left: 0 !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }

/* Set a $size margin to all sides at $breakpoint */
.m-1 { margin: 4px !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-1 { margin-top: 4px !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-1 { margin-right: 4px !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-1 { margin-bottom: 4px !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-1 { margin-left: 4px !important; }

/* Set a negative $size margin on top at $breakpoint */
.mt-n1 { margin-top: -4px !important; }

/* Set a negative $size margin on the right at $breakpoint */
.mr-n1 { margin-right: -4px !important; }

/* Set a negative $size margin on the bottom at $breakpoint */
.mb-n1 { margin-bottom: -4px !important; }

/* Set a negative $size margin on the left at $breakpoint */
.ml-n1 { margin-left: -4px !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-1 { margin-right: 4px !important; margin-left: 4px !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-1 { margin-top: 4px !important; margin-bottom: 4px !important; }

/* Set a $size margin to all sides at $breakpoint */
.m-2 { margin: 8px !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-2 { margin-top: 8px !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-2 { margin-right: 8px !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-2 { margin-bottom: 8px !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-2 { margin-left: 8px !important; }

/* Set a negative $size margin on top at $breakpoint */
.mt-n2 { margin-top: -8px !important; }

/* Set a negative $size margin on the right at $breakpoint */
.mr-n2 { margin-right: -8px !important; }

/* Set a negative $size margin on the bottom at $breakpoint */
.mb-n2 { margin-bottom: -8px !important; }

/* Set a negative $size margin on the left at $breakpoint */
.ml-n2 { margin-left: -8px !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-2 { margin-right: 8px !important; margin-left: 8px !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-2 { margin-top: 8px !important; margin-bottom: 8px !important; }

/* Set a $size margin to all sides at $breakpoint */
.m-3 { margin: 16px !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-3 { margin-top: 16px !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-3 { margin-right: 16px !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-3 { margin-bottom: 16px !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-3 { margin-left: 16px !important; }

/* Set a negative $size margin on top at $breakpoint */
.mt-n3 { margin-top: -16px !important; }

/* Set a negative $size margin on the right at $breakpoint */
.mr-n3 { margin-right: -16px !important; }

/* Set a negative $size margin on the bottom at $breakpoint */
.mb-n3 { margin-bottom: -16px !important; }

/* Set a negative $size margin on the left at $breakpoint */
.ml-n3 { margin-left: -16px !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-3 { margin-right: 16px !important; margin-left: 16px !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-3 { margin-top: 16px !important; margin-bottom: 16px !important; }

/* Set a $size margin to all sides at $breakpoint */
.m-4 { margin: 24px !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-4 { margin-top: 24px !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-4 { margin-right: 24px !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-4 { margin-bottom: 24px !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-4 { margin-left: 24px !important; }

/* Set a negative $size margin on top at $breakpoint */
.mt-n4 { margin-top: -24px !important; }

/* Set a negative $size margin on the right at $breakpoint */
.mr-n4 { margin-right: -24px !important; }

/* Set a negative $size margin on the bottom at $breakpoint */
.mb-n4 { margin-bottom: -24px !important; }

/* Set a negative $size margin on the left at $breakpoint */
.ml-n4 { margin-left: -24px !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-4 { margin-right: 24px !important; margin-left: 24px !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-4 { margin-top: 24px !important; margin-bottom: 24px !important; }

/* Set a $size margin to all sides at $breakpoint */
.m-5 { margin: 32px !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-5 { margin-top: 32px !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-5 { margin-right: 32px !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-5 { margin-bottom: 32px !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-5 { margin-left: 32px !important; }

/* Set a negative $size margin on top at $breakpoint */
.mt-n5 { margin-top: -32px !important; }

/* Set a negative $size margin on the right at $breakpoint */
.mr-n5 { margin-right: -32px !important; }

/* Set a negative $size margin on the bottom at $breakpoint */
.mb-n5 { margin-bottom: -32px !important; }

/* Set a negative $size margin on the left at $breakpoint */
.ml-n5 { margin-left: -32px !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-5 { margin-right: 32px !important; margin-left: 32px !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-5 { margin-top: 32px !important; margin-bottom: 32px !important; }

/* Set a $size margin to all sides at $breakpoint */
.m-6 { margin: 40px !important; }

/* Set a $size margin on the top at $breakpoint */
.mt-6 { margin-top: 40px !important; }

/* Set a $size margin on the right at $breakpoint */
.mr-6 { margin-right: 40px !important; }

/* Set a $size margin on the bottom at $breakpoint */
.mb-6 { margin-bottom: 40px !important; }

/* Set a $size margin on the left at $breakpoint */
.ml-6 { margin-left: 40px !important; }

/* Set a negative $size margin on top at $breakpoint */
.mt-n6 { margin-top: -40px !important; }

/* Set a negative $size margin on the right at $breakpoint */
.mr-n6 { margin-right: -40px !important; }

/* Set a negative $size margin on the bottom at $breakpoint */
.mb-n6 { margin-bottom: -40px !important; }

/* Set a negative $size margin on the left at $breakpoint */
.ml-n6 { margin-left: -40px !important; }

/* Set a $size margin on the left & right at $breakpoint */
.mx-6 { margin-right: 40px !important; margin-left: 40px !important; }

/* Set a $size margin on the top & bottom at $breakpoint */
.my-6 { margin-top: 40px !important; margin-bottom: 40px !important; }

/* responsive horizontal auto margins */
.mx-auto { margin-right: auto !important; margin-left: auto !important; }

@media (min-width: 544px) { /* Set a $size margin to all sides at $breakpoint */
  .m-sm-0 { margin: 0 !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-0 { margin-top: 0 !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-0 { margin-right: 0 !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-0 { margin-bottom: 0 !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-0 { margin-left: 0 !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-0 { margin-right: 0 !important; margin-left: 0 !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-sm-1 { margin: 4px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-1 { margin-top: 4px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-1 { margin-right: 4px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-1 { margin-bottom: 4px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-1 { margin-left: 4px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-sm-n1 { margin-top: -4px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-sm-n1 { margin-right: -4px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-sm-n1 { margin-bottom: -4px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-sm-n1 { margin-left: -4px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-1 { margin-right: 4px !important; margin-left: 4px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-1 { margin-top: 4px !important; margin-bottom: 4px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-sm-2 { margin: 8px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-2 { margin-top: 8px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-2 { margin-right: 8px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-2 { margin-bottom: 8px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-2 { margin-left: 8px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-sm-n2 { margin-top: -8px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-sm-n2 { margin-right: -8px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-sm-n2 { margin-bottom: -8px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-sm-n2 { margin-left: -8px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-2 { margin-right: 8px !important; margin-left: 8px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-2 { margin-top: 8px !important; margin-bottom: 8px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-sm-3 { margin: 16px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-3 { margin-top: 16px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-3 { margin-right: 16px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-3 { margin-bottom: 16px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-3 { margin-left: 16px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-sm-n3 { margin-top: -16px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-sm-n3 { margin-right: -16px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-sm-n3 { margin-bottom: -16px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-sm-n3 { margin-left: -16px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-3 { margin-right: 16px !important; margin-left: 16px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-3 { margin-top: 16px !important; margin-bottom: 16px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-sm-4 { margin: 24px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-4 { margin-top: 24px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-4 { margin-right: 24px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-4 { margin-bottom: 24px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-4 { margin-left: 24px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-sm-n4 { margin-top: -24px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-sm-n4 { margin-right: -24px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-sm-n4 { margin-bottom: -24px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-sm-n4 { margin-left: -24px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-4 { margin-right: 24px !important; margin-left: 24px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-4 { margin-top: 24px !important; margin-bottom: 24px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-sm-5 { margin: 32px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-5 { margin-top: 32px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-5 { margin-right: 32px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-5 { margin-bottom: 32px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-5 { margin-left: 32px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-sm-n5 { margin-top: -32px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-sm-n5 { margin-right: -32px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-sm-n5 { margin-bottom: -32px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-sm-n5 { margin-left: -32px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-5 { margin-right: 32px !important; margin-left: 32px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-5 { margin-top: 32px !important; margin-bottom: 32px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-sm-6 { margin: 40px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-sm-6 { margin-top: 40px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-sm-6 { margin-right: 40px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-sm-6 { margin-bottom: 40px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-sm-6 { margin-left: 40px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-sm-n6 { margin-top: -40px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-sm-n6 { margin-right: -40px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-sm-n6 { margin-bottom: -40px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-sm-n6 { margin-left: -40px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-sm-6 { margin-right: 40px !important; margin-left: 40px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-sm-6 { margin-top: 40px !important; margin-bottom: 40px !important; }
  /* responsive horizontal auto margins */
  .mx-sm-auto { margin-right: auto !important; margin-left: auto !important; } }
@media (min-width: 768px) { /* Set a $size margin to all sides at $breakpoint */
  .m-md-0 { margin: 0 !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-0 { margin-top: 0 !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-0 { margin-right: 0 !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-0 { margin-bottom: 0 !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-0 { margin-left: 0 !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-0 { margin-right: 0 !important; margin-left: 0 !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-md-1 { margin: 4px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-1 { margin-top: 4px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-1 { margin-right: 4px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-1 { margin-bottom: 4px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-1 { margin-left: 4px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-md-n1 { margin-top: -4px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-md-n1 { margin-right: -4px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-md-n1 { margin-bottom: -4px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-md-n1 { margin-left: -4px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-1 { margin-right: 4px !important; margin-left: 4px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-1 { margin-top: 4px !important; margin-bottom: 4px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-md-2 { margin: 8px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-2 { margin-top: 8px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-2 { margin-right: 8px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-2 { margin-bottom: 8px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-2 { margin-left: 8px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-md-n2 { margin-top: -8px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-md-n2 { margin-right: -8px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-md-n2 { margin-bottom: -8px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-md-n2 { margin-left: -8px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-2 { margin-right: 8px !important; margin-left: 8px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-2 { margin-top: 8px !important; margin-bottom: 8px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-md-3 { margin: 16px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-3 { margin-top: 16px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-3 { margin-right: 16px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-3 { margin-bottom: 16px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-3 { margin-left: 16px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-md-n3 { margin-top: -16px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-md-n3 { margin-right: -16px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-md-n3 { margin-bottom: -16px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-md-n3 { margin-left: -16px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-3 { margin-right: 16px !important; margin-left: 16px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-3 { margin-top: 16px !important; margin-bottom: 16px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-md-4 { margin: 24px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-4 { margin-top: 24px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-4 { margin-right: 24px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-4 { margin-bottom: 24px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-4 { margin-left: 24px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-md-n4 { margin-top: -24px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-md-n4 { margin-right: -24px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-md-n4 { margin-bottom: -24px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-md-n4 { margin-left: -24px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-4 { margin-right: 24px !important; margin-left: 24px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-4 { margin-top: 24px !important; margin-bottom: 24px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-md-5 { margin: 32px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-5 { margin-top: 32px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-5 { margin-right: 32px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-5 { margin-bottom: 32px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-5 { margin-left: 32px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-md-n5 { margin-top: -32px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-md-n5 { margin-right: -32px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-md-n5 { margin-bottom: -32px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-md-n5 { margin-left: -32px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-5 { margin-right: 32px !important; margin-left: 32px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-5 { margin-top: 32px !important; margin-bottom: 32px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-md-6 { margin: 40px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-md-6 { margin-top: 40px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-md-6 { margin-right: 40px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-md-6 { margin-bottom: 40px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-md-6 { margin-left: 40px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-md-n6 { margin-top: -40px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-md-n6 { margin-right: -40px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-md-n6 { margin-bottom: -40px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-md-n6 { margin-left: -40px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-md-6 { margin-right: 40px !important; margin-left: 40px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-md-6 { margin-top: 40px !important; margin-bottom: 40px !important; }
  /* responsive horizontal auto margins */
  .mx-md-auto { margin-right: auto !important; margin-left: auto !important; } }
@media (min-width: 1012px) { /* Set a $size margin to all sides at $breakpoint */
  .m-lg-0 { margin: 0 !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-0 { margin-top: 0 !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-0 { margin-right: 0 !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-0 { margin-bottom: 0 !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-0 { margin-left: 0 !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-0 { margin-right: 0 !important; margin-left: 0 !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-lg-1 { margin: 4px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-1 { margin-top: 4px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-1 { margin-right: 4px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-1 { margin-bottom: 4px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-1 { margin-left: 4px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-lg-n1 { margin-top: -4px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-lg-n1 { margin-right: -4px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-lg-n1 { margin-bottom: -4px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-lg-n1 { margin-left: -4px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-1 { margin-right: 4px !important; margin-left: 4px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-1 { margin-top: 4px !important; margin-bottom: 4px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-lg-2 { margin: 8px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-2 { margin-top: 8px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-2 { margin-right: 8px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-2 { margin-bottom: 8px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-2 { margin-left: 8px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-lg-n2 { margin-top: -8px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-lg-n2 { margin-right: -8px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-lg-n2 { margin-bottom: -8px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-lg-n2 { margin-left: -8px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-2 { margin-right: 8px !important; margin-left: 8px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-2 { margin-top: 8px !important; margin-bottom: 8px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-lg-3 { margin: 16px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-3 { margin-top: 16px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-3 { margin-right: 16px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-3 { margin-bottom: 16px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-3 { margin-left: 16px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-lg-n3 { margin-top: -16px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-lg-n3 { margin-right: -16px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-lg-n3 { margin-bottom: -16px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-lg-n3 { margin-left: -16px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-3 { margin-right: 16px !important; margin-left: 16px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-3 { margin-top: 16px !important; margin-bottom: 16px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-lg-4 { margin: 24px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-4 { margin-top: 24px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-4 { margin-right: 24px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-4 { margin-bottom: 24px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-4 { margin-left: 24px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-lg-n4 { margin-top: -24px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-lg-n4 { margin-right: -24px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-lg-n4 { margin-bottom: -24px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-lg-n4 { margin-left: -24px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-4 { margin-right: 24px !important; margin-left: 24px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-4 { margin-top: 24px !important; margin-bottom: 24px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-lg-5 { margin: 32px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-5 { margin-top: 32px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-5 { margin-right: 32px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-5 { margin-bottom: 32px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-5 { margin-left: 32px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-lg-n5 { margin-top: -32px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-lg-n5 { margin-right: -32px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-lg-n5 { margin-bottom: -32px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-lg-n5 { margin-left: -32px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-5 { margin-right: 32px !important; margin-left: 32px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-5 { margin-top: 32px !important; margin-bottom: 32px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-lg-6 { margin: 40px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-lg-6 { margin-top: 40px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-lg-6 { margin-right: 40px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-lg-6 { margin-bottom: 40px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-lg-6 { margin-left: 40px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-lg-n6 { margin-top: -40px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-lg-n6 { margin-right: -40px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-lg-n6 { margin-bottom: -40px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-lg-n6 { margin-left: -40px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-lg-6 { margin-right: 40px !important; margin-left: 40px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-lg-6 { margin-top: 40px !important; margin-bottom: 40px !important; }
  /* responsive horizontal auto margins */
  .mx-lg-auto { margin-right: auto !important; margin-left: auto !important; } }
@media (min-width: 1280px) { /* Set a $size margin to all sides at $breakpoint */
  .m-xl-0 { margin: 0 !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-0 { margin-top: 0 !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-0 { margin-right: 0 !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-0 { margin-bottom: 0 !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-0 { margin-left: 0 !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-0 { margin-right: 0 !important; margin-left: 0 !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-xl-1 { margin: 4px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-1 { margin-top: 4px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-1 { margin-right: 4px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-1 { margin-bottom: 4px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-1 { margin-left: 4px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-xl-n1 { margin-top: -4px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-xl-n1 { margin-right: -4px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-xl-n1 { margin-bottom: -4px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-xl-n1 { margin-left: -4px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-1 { margin-right: 4px !important; margin-left: 4px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-1 { margin-top: 4px !important; margin-bottom: 4px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-xl-2 { margin: 8px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-2 { margin-top: 8px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-2 { margin-right: 8px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-2 { margin-bottom: 8px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-2 { margin-left: 8px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-xl-n2 { margin-top: -8px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-xl-n2 { margin-right: -8px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-xl-n2 { margin-bottom: -8px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-xl-n2 { margin-left: -8px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-2 { margin-right: 8px !important; margin-left: 8px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-2 { margin-top: 8px !important; margin-bottom: 8px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-xl-3 { margin: 16px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-3 { margin-top: 16px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-3 { margin-right: 16px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-3 { margin-bottom: 16px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-3 { margin-left: 16px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-xl-n3 { margin-top: -16px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-xl-n3 { margin-right: -16px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-xl-n3 { margin-bottom: -16px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-xl-n3 { margin-left: -16px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-3 { margin-right: 16px !important; margin-left: 16px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-3 { margin-top: 16px !important; margin-bottom: 16px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-xl-4 { margin: 24px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-4 { margin-top: 24px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-4 { margin-right: 24px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-4 { margin-bottom: 24px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-4 { margin-left: 24px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-xl-n4 { margin-top: -24px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-xl-n4 { margin-right: -24px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-xl-n4 { margin-bottom: -24px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-xl-n4 { margin-left: -24px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-4 { margin-right: 24px !important; margin-left: 24px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-4 { margin-top: 24px !important; margin-bottom: 24px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-xl-5 { margin: 32px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-5 { margin-top: 32px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-5 { margin-right: 32px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-5 { margin-bottom: 32px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-5 { margin-left: 32px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-xl-n5 { margin-top: -32px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-xl-n5 { margin-right: -32px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-xl-n5 { margin-bottom: -32px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-xl-n5 { margin-left: -32px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-5 { margin-right: 32px !important; margin-left: 32px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-5 { margin-top: 32px !important; margin-bottom: 32px !important; }
  /* Set a $size margin to all sides at $breakpoint */
  .m-xl-6 { margin: 40px !important; }
  /* Set a $size margin on the top at $breakpoint */
  .mt-xl-6 { margin-top: 40px !important; }
  /* Set a $size margin on the right at $breakpoint */
  .mr-xl-6 { margin-right: 40px !important; }
  /* Set a $size margin on the bottom at $breakpoint */
  .mb-xl-6 { margin-bottom: 40px !important; }
  /* Set a $size margin on the left at $breakpoint */
  .ml-xl-6 { margin-left: 40px !important; }
  /* Set a negative $size margin on top at $breakpoint */
  .mt-xl-n6 { margin-top: -40px !important; }
  /* Set a negative $size margin on the right at $breakpoint */
  .mr-xl-n6 { margin-right: -40px !important; }
  /* Set a negative $size margin on the bottom at $breakpoint */
  .mb-xl-n6 { margin-bottom: -40px !important; }
  /* Set a negative $size margin on the left at $breakpoint */
  .ml-xl-n6 { margin-left: -40px !important; }
  /* Set a $size margin on the left & right at $breakpoint */
  .mx-xl-6 { margin-right: 40px !important; margin-left: 40px !important; }
  /* Set a $size margin on the top & bottom at $breakpoint */
  .my-xl-6 { margin-top: 40px !important; margin-bottom: 40px !important; }
  /* responsive horizontal auto margins */
  .mx-xl-auto { margin-right: auto !important; margin-left: auto !important; } }
/* Set a $size padding to all sides at $breakpoint */
.p-0 { padding: 0 !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-0 { padding-top: 0 !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-0 { padding-right: 0 !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-0 { padding-bottom: 0 !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-0 { padding-left: 0 !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-0 { padding-right: 0 !important; padding-left: 0 !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }

/* Set a $size padding to all sides at $breakpoint */
.p-1 { padding: 4px !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-1 { padding-top: 4px !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-1 { padding-right: 4px !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-1 { padding-bottom: 4px !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-1 { padding-left: 4px !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-1 { padding-right: 4px !important; padding-left: 4px !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-1 { padding-top: 4px !important; padding-bottom: 4px !important; }

/* Set a $size padding to all sides at $breakpoint */
.p-2 { padding: 8px !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-2 { padding-top: 8px !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-2 { padding-right: 8px !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-2 { padding-bottom: 8px !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-2 { padding-left: 8px !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-2 { padding-right: 8px !important; padding-left: 8px !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-2 { padding-top: 8px !important; padding-bottom: 8px !important; }

/* Set a $size padding to all sides at $breakpoint */
.p-3 { padding: 16px !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-3 { padding-top: 16px !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-3 { padding-right: 16px !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-3 { padding-bottom: 16px !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-3 { padding-left: 16px !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-3 { padding-right: 16px !important; padding-left: 16px !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-3 { padding-top: 16px !important; padding-bottom: 16px !important; }

/* Set a $size padding to all sides at $breakpoint */
.p-4 { padding: 24px !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-4 { padding-top: 24px !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-4 { padding-right: 24px !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-4 { padding-bottom: 24px !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-4 { padding-left: 24px !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-4 { padding-right: 24px !important; padding-left: 24px !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-4 { padding-top: 24px !important; padding-bottom: 24px !important; }

/* Set a $size padding to all sides at $breakpoint */
.p-5 { padding: 32px !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-5 { padding-top: 32px !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-5 { padding-right: 32px !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-5 { padding-bottom: 32px !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-5 { padding-left: 32px !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-5 { padding-right: 32px !important; padding-left: 32px !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-5 { padding-top: 32px !important; padding-bottom: 32px !important; }

/* Set a $size padding to all sides at $breakpoint */
.p-6 { padding: 40px !important; }

/* Set a $size padding to the top at $breakpoint */
.pt-6 { padding-top: 40px !important; }

/* Set a $size padding to the right at $breakpoint */
.pr-6 { padding-right: 40px !important; }

/* Set a $size padding to the bottom at $breakpoint */
.pb-6 { padding-bottom: 40px !important; }

/* Set a $size padding to the left at $breakpoint */
.pl-6 { padding-left: 40px !important; }

/* Set a $size padding to the left & right at $breakpoint */
.px-6 { padding-right: 40px !important; padding-left: 40px !important; }

/* Set a $size padding to the top & bottom at $breakpoint */
.py-6 { padding-top: 40px !important; padding-bottom: 40px !important; }

@media (min-width: 544px) { /* Set a $size padding to all sides at $breakpoint */
  .p-sm-0 { padding: 0 !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-0 { padding-top: 0 !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-0 { padding-right: 0 !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-0 { padding-bottom: 0 !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-0 { padding-left: 0 !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-0 { padding-right: 0 !important; padding-left: 0 !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-sm-1 { padding: 4px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-1 { padding-top: 4px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-1 { padding-right: 4px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-1 { padding-bottom: 4px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-1 { padding-left: 4px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-1 { padding-right: 4px !important; padding-left: 4px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-1 { padding-top: 4px !important; padding-bottom: 4px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-sm-2 { padding: 8px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-2 { padding-top: 8px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-2 { padding-right: 8px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-2 { padding-bottom: 8px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-2 { padding-left: 8px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-2 { padding-right: 8px !important; padding-left: 8px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-2 { padding-top: 8px !important; padding-bottom: 8px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-sm-3 { padding: 16px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-3 { padding-top: 16px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-3 { padding-right: 16px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-3 { padding-bottom: 16px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-3 { padding-left: 16px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-3 { padding-right: 16px !important; padding-left: 16px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-3 { padding-top: 16px !important; padding-bottom: 16px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-sm-4 { padding: 24px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-4 { padding-top: 24px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-4 { padding-right: 24px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-4 { padding-bottom: 24px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-4 { padding-left: 24px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-4 { padding-right: 24px !important; padding-left: 24px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-4 { padding-top: 24px !important; padding-bottom: 24px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-sm-5 { padding: 32px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-5 { padding-top: 32px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-5 { padding-right: 32px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-5 { padding-bottom: 32px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-5 { padding-left: 32px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-5 { padding-right: 32px !important; padding-left: 32px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-5 { padding-top: 32px !important; padding-bottom: 32px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-sm-6 { padding: 40px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-sm-6 { padding-top: 40px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-sm-6 { padding-right: 40px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-sm-6 { padding-bottom: 40px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-sm-6 { padding-left: 40px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-sm-6 { padding-right: 40px !important; padding-left: 40px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-sm-6 { padding-top: 40px !important; padding-bottom: 40px !important; } }
@media (min-width: 768px) { /* Set a $size padding to all sides at $breakpoint */
  .p-md-0 { padding: 0 !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-0 { padding-top: 0 !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-0 { padding-right: 0 !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-0 { padding-bottom: 0 !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-0 { padding-left: 0 !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-0 { padding-right: 0 !important; padding-left: 0 !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-md-1 { padding: 4px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-1 { padding-top: 4px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-1 { padding-right: 4px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-1 { padding-bottom: 4px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-1 { padding-left: 4px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-1 { padding-right: 4px !important; padding-left: 4px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-1 { padding-top: 4px !important; padding-bottom: 4px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-md-2 { padding: 8px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-2 { padding-top: 8px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-2 { padding-right: 8px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-2 { padding-bottom: 8px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-2 { padding-left: 8px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-2 { padding-right: 8px !important; padding-left: 8px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-2 { padding-top: 8px !important; padding-bottom: 8px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-md-3 { padding: 16px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-3 { padding-top: 16px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-3 { padding-right: 16px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-3 { padding-bottom: 16px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-3 { padding-left: 16px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-3 { padding-right: 16px !important; padding-left: 16px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-3 { padding-top: 16px !important; padding-bottom: 16px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-md-4 { padding: 24px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-4 { padding-top: 24px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-4 { padding-right: 24px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-4 { padding-bottom: 24px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-4 { padding-left: 24px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-4 { padding-right: 24px !important; padding-left: 24px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-4 { padding-top: 24px !important; padding-bottom: 24px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-md-5 { padding: 32px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-5 { padding-top: 32px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-5 { padding-right: 32px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-5 { padding-bottom: 32px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-5 { padding-left: 32px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-5 { padding-right: 32px !important; padding-left: 32px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-5 { padding-top: 32px !important; padding-bottom: 32px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-md-6 { padding: 40px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-md-6 { padding-top: 40px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-md-6 { padding-right: 40px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-md-6 { padding-bottom: 40px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-md-6 { padding-left: 40px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-md-6 { padding-right: 40px !important; padding-left: 40px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-md-6 { padding-top: 40px !important; padding-bottom: 40px !important; } }
@media (min-width: 1012px) { /* Set a $size padding to all sides at $breakpoint */
  .p-lg-0 { padding: 0 !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-0 { padding-top: 0 !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-0 { padding-right: 0 !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-0 { padding-bottom: 0 !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-0 { padding-left: 0 !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-0 { padding-right: 0 !important; padding-left: 0 !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-lg-1 { padding: 4px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-1 { padding-top: 4px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-1 { padding-right: 4px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-1 { padding-bottom: 4px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-1 { padding-left: 4px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-1 { padding-right: 4px !important; padding-left: 4px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-1 { padding-top: 4px !important; padding-bottom: 4px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-lg-2 { padding: 8px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-2 { padding-top: 8px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-2 { padding-right: 8px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-2 { padding-bottom: 8px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-2 { padding-left: 8px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-2 { padding-right: 8px !important; padding-left: 8px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-2 { padding-top: 8px !important; padding-bottom: 8px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-lg-3 { padding: 16px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-3 { padding-top: 16px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-3 { padding-right: 16px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-3 { padding-bottom: 16px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-3 { padding-left: 16px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-3 { padding-right: 16px !important; padding-left: 16px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-3 { padding-top: 16px !important; padding-bottom: 16px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-lg-4 { padding: 24px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-4 { padding-top: 24px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-4 { padding-right: 24px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-4 { padding-bottom: 24px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-4 { padding-left: 24px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-4 { padding-right: 24px !important; padding-left: 24px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-4 { padding-top: 24px !important; padding-bottom: 24px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-lg-5 { padding: 32px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-5 { padding-top: 32px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-5 { padding-right: 32px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-5 { padding-bottom: 32px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-5 { padding-left: 32px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-5 { padding-right: 32px !important; padding-left: 32px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-5 { padding-top: 32px !important; padding-bottom: 32px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-lg-6 { padding: 40px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-lg-6 { padding-top: 40px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-lg-6 { padding-right: 40px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-lg-6 { padding-bottom: 40px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-lg-6 { padding-left: 40px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-lg-6 { padding-right: 40px !important; padding-left: 40px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-lg-6 { padding-top: 40px !important; padding-bottom: 40px !important; } }
@media (min-width: 1280px) { /* Set a $size padding to all sides at $breakpoint */
  .p-xl-0 { padding: 0 !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-0 { padding-top: 0 !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-0 { padding-right: 0 !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-0 { padding-bottom: 0 !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-0 { padding-left: 0 !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-0 { padding-right: 0 !important; padding-left: 0 !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-xl-1 { padding: 4px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-1 { padding-top: 4px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-1 { padding-right: 4px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-1 { padding-bottom: 4px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-1 { padding-left: 4px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-1 { padding-right: 4px !important; padding-left: 4px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-1 { padding-top: 4px !important; padding-bottom: 4px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-xl-2 { padding: 8px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-2 { padding-top: 8px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-2 { padding-right: 8px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-2 { padding-bottom: 8px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-2 { padding-left: 8px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-2 { padding-right: 8px !important; padding-left: 8px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-2 { padding-top: 8px !important; padding-bottom: 8px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-xl-3 { padding: 16px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-3 { padding-top: 16px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-3 { padding-right: 16px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-3 { padding-bottom: 16px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-3 { padding-left: 16px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-3 { padding-right: 16px !important; padding-left: 16px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-3 { padding-top: 16px !important; padding-bottom: 16px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-xl-4 { padding: 24px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-4 { padding-top: 24px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-4 { padding-right: 24px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-4 { padding-bottom: 24px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-4 { padding-left: 24px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-4 { padding-right: 24px !important; padding-left: 24px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-4 { padding-top: 24px !important; padding-bottom: 24px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-xl-5 { padding: 32px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-5 { padding-top: 32px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-5 { padding-right: 32px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-5 { padding-bottom: 32px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-5 { padding-left: 32px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-5 { padding-right: 32px !important; padding-left: 32px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-5 { padding-top: 32px !important; padding-bottom: 32px !important; }
  /* Set a $size padding to all sides at $breakpoint */
  .p-xl-6 { padding: 40px !important; }
  /* Set a $size padding to the top at $breakpoint */
  .pt-xl-6 { padding-top: 40px !important; }
  /* Set a $size padding to the right at $breakpoint */
  .pr-xl-6 { padding-right: 40px !important; }
  /* Set a $size padding to the bottom at $breakpoint */
  .pb-xl-6 { padding-bottom: 40px !important; }
  /* Set a $size padding to the left at $breakpoint */
  .pl-xl-6 { padding-left: 40px !important; }
  /* Set a $size padding to the left & right at $breakpoint */
  .px-xl-6 { padding-right: 40px !important; padding-left: 40px !important; }
  /* Set a $size padding to the top & bottom at $breakpoint */
  .py-xl-6 { padding-top: 40px !important; padding-bottom: 40px !important; } }
.p-responsive { padding-right: 16px !important; padding-left: 16px !important; }
@media (min-width: 544px) { .p-responsive { padding-right: 40px !important; padding-left: 40px !important; } }
@media (min-width: 1012px) { .p-responsive { padding-right: 16px !important; padding-left: 16px !important; } }

/* Set the font size to 26px */
.h1 { font-size: 26px !important; }
@media (min-width: 768px) { .h1 { font-size: 32px !important; } }

/* Set the font size to 22px */
.h2 { font-size: 22px !important; }
@media (min-width: 768px) { .h2 { font-size: 24px !important; } }

/* Set the font size to 18px */
.h3 { font-size: 18px !important; }
@media (min-width: 768px) { .h3 { font-size: 20px !important; } }

/* Set the font size to 16px */
.h4 { font-size: 16px !important; }

/* Set the font size to 14px */
.h5 { font-size: 14px !important; }

/* Set the font size to 12px */
.h6 { font-size: 12px !important; }

.h1, .h2, .h3, .h4, .h5, .h6 { font-weight: 600 !important; }

/* Set the font size to 26px */
.f1 { font-size: 26px !important; }
@media (min-width: 768px) { .f1 { font-size: 32px !important; } }

/* Set the font size to 22px */
.f2 { font-size: 22px !important; }
@media (min-width: 768px) { .f2 { font-size: 24px !important; } }

/* Set the font size to 18px */
.f3 { font-size: 18px !important; }
@media (min-width: 768px) { .f3 { font-size: 20px !important; } }

/* Set the font size to 16px */
.f4 { font-size: 16px !important; }
@media (min-width: 768px) { .f4 { font-size: 16px !important; } }

/* Set the font size to 14px */
.f5 { font-size: 14px !important; }

/* Set the font size to 12px */
.f6 { font-size: 12px !important; }

/* Set the font size to 40px and weight to light */
.f00-light { font-size: 40px !important; font-weight: 300 !important; }
@media (min-width: 768px) { .f00-light { font-size: 48px !important; } }

/* Set the font size to 32px and weight to light */
.f0-light { font-size: 32px !important; font-weight: 300 !important; }
@media (min-width: 768px) { .f0-light { font-size: 40px !important; } }

/* Set the font size to 26px and weight to light */
.f1-light { font-size: 26px !important; font-weight: 300 !important; }
@media (min-width: 768px) { .f1-light { font-size: 32px !important; } }

/* Set the font size to 22px and weight to light */
.f2-light { font-size: 22px !important; font-weight: 300 !important; }
@media (min-width: 768px) { .f2-light { font-size: 24px !important; } }

/* Set the font size to 18px and weight to light */
.f3-light { font-size: 18px !important; font-weight: 300 !important; }
@media (min-width: 768px) { .f3-light { font-size: 20px !important; } }

/* Set the font size to ${#h6-size} */
.text-small { font-size: 12px !important; }

/* Large leading paragraphs */
.lead { margin-bottom: 30px; font-size: 20px; font-weight: 300; color: #586069; }

/* Set the line height to ultra condensed */
.lh-condensed-ultra { line-height: 1 !important; }

/* Set the line height to condensed */
.lh-condensed { line-height: 1.25 !important; }

/* Set the line height to default */
.lh-default { line-height: 1.5 !important; }

/* Set the line height to zero */
.lh-0 { line-height: 0 !important; }

/* Text align to the right */
.text-right { text-align: right !important; }

/* Text align to the left */
.text-left { text-align: left !important; }

/* Text align to the center */
.text-center { text-align: center !important; }

@media (min-width: 544px) { /* Text align to the right */
  .text-sm-right { text-align: right !important; }
  /* Text align to the left */
  .text-sm-left { text-align: left !important; }
  /* Text align to the center */
  .text-sm-center { text-align: center !important; } }
@media (min-width: 768px) { /* Text align to the right */
  .text-md-right { text-align: right !important; }
  /* Text align to the left */
  .text-md-left { text-align: left !important; }
  /* Text align to the center */
  .text-md-center { text-align: center !important; } }
@media (min-width: 1012px) { /* Text align to the right */
  .text-lg-right { text-align: right !important; }
  /* Text align to the left */
  .text-lg-left { text-align: left !important; }
  /* Text align to the center */
  .text-lg-center { text-align: center !important; } }
@media (min-width: 1280px) { /* Text align to the right */
  .text-xl-right { text-align: right !important; }
  /* Text align to the left */
  .text-xl-left { text-align: left !important; }
  /* Text align to the center */
  .text-xl-center { text-align: center !important; } }
/* Set the font weight to normal */
.text-normal { font-weight: 400 !important; }

/* Set the font weight to bold */
.text-bold { font-weight: 600 !important; }

/* Set the font to italic */
.text-italic { font-style: italic !important; }

/* Make text uppercase */
.text-uppercase { text-transform: uppercase !important; }

/* Underline text */
.text-underline { text-decoration: underline !important; }

/* Don't underline text */
.no-underline { text-decoration: none !important; }

/* Don't wrap white space */
.no-wrap { white-space: nowrap !important; }

/* Normal white space */
.ws-normal { white-space: normal !important; }

/* Allow long lines with no spaces to line break */
.wb-break-all { word-break: break-all !important; }

.text-emphasized { font-weight: 600; color: #24292e; }

.list-style-none { list-style: none !important; }

/* Add a dark text shadow */
.text-shadow-dark { text-shadow: 0 1px 1px rgba(27, 31, 35, 0.25), 0 1px 25px rgba(27, 31, 35, 0.75); }

/* Add a light text shadow */
.text-shadow-light { text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5); }

/* Set to monospace font */
.text-mono { font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace; }

/* Disallow user from selecting text */
.user-select-none { user-select: none !important; }

.d-block { display: block !important; }

.d-flex { display: flex !important; }

.d-inline { display: inline !important; }

.d-inline-block { display: inline-block !important; }

.d-inline-flex { display: inline-flex !important; }

.d-none { display: none !important; }

.d-table { display: table !important; }

.d-table-cell { display: table-cell !important; }

@media (min-width: 544px) { .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
  .d-sm-none { display: none !important; }
  .d-sm-table { display: table !important; }
  .d-sm-table-cell { display: table-cell !important; } }
@media (min-width: 768px) { .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-inline-flex { display: inline-flex !important; }
  .d-md-none { display: none !important; }
  .d-md-table { display: table !important; }
  .d-md-table-cell { display: table-cell !important; } }
@media (min-width: 1012px) { .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
  .d-lg-none { display: none !important; }
  .d-lg-table { display: table !important; }
  .d-lg-table-cell { display: table-cell !important; } }
@media (min-width: 1280px) { .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
  .d-xl-none { display: none !important; }
  .d-xl-table { display: table !important; }
  .d-xl-table-cell { display: table-cell !important; } }
.v-hidden { visibility: hidden !important; }

.v-visible { visibility: visible !important; }

@media (max-width: 544px) { .hide-sm { display: none !important; } }
@media (min-width: 544px) and (max-width: 768px) { .hide-md { display: none !important; } }
@media (min-width: 768px) and (max-width: 1012px) { .hide-lg { display: none !important; } }
@media (min-width: 1012px) { .hide-xl { display: none !important; } }
/* Set the table-layout to fixed */
.table-fixed { table-layout: fixed !important; }

.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; overflow: hidden; clip: rect(0, 0, 0, 0); word-wrap: normal; border: 0; }

.show-on-focus { position: absolute; width: 1px; height: 1px; margin: 0; overflow: hidden; clip: rect(1px, 1px, 1px, 1px); }
.show-on-focus:focus { z-index: 20; width: auto; height: auto; clip: auto; }

.container { width: 980px; margin-right: auto; margin-left: auto; }
.container::before { display: table; content: ""; }
.container::after { display: table; clear: both; content: ""; }

.container-md { max-width: 768px; margin-right: auto; margin-left: auto; }

.container-lg { max-width: 1012px; margin-right: auto; margin-left: auto; }

.container-xl { max-width: 1280px; margin-right: auto; margin-left: auto; }

.columns { margin-right: -10px; margin-left: -10px; }
.columns::before { display: table; content: ""; }
.columns::after { display: table; clear: both; content: ""; }

.column { float: left; padding-right: 10px; padding-left: 10px; }

.one-third { width: 33.333333%; }

.two-thirds { width: 66.666667%; }

.one-fourth { width: 25%; }

.one-half { width: 50%; }

.three-fourths { width: 75%; }

.one-fifth { width: 20%; }

.four-fifths { width: 80%; }

.centered { display: block; float: none; margin-right: auto; margin-left: auto; }

.col-1 { width: 8.3333333333%; }

.col-2 { width: 16.6666666667%; }

.col-3 { width: 25%; }

.col-4 { width: 33.3333333333%; }

.col-5 { width: 41.6666666667%; }

.col-6 { width: 50%; }

.col-7 { width: 58.3333333333%; }

.col-8 { width: 66.6666666667%; }

.col-9 { width: 75%; }

.col-10 { width: 83.3333333333%; }

.col-11 { width: 91.6666666667%; }

.col-12 { width: 100%; }

@media (min-width: 544px) { .col-sm-1 { width: 8.3333333333%; }
  .col-sm-2 { width: 16.6666666667%; }
  .col-sm-3 { width: 25%; }
  .col-sm-4 { width: 33.3333333333%; }
  .col-sm-5 { width: 41.6666666667%; }
  .col-sm-6 { width: 50%; }
  .col-sm-7 { width: 58.3333333333%; }
  .col-sm-8 { width: 66.6666666667%; }
  .col-sm-9 { width: 75%; }
  .col-sm-10 { width: 83.3333333333%; }
  .col-sm-11 { width: 91.6666666667%; }
  .col-sm-12 { width: 100%; } }
@media (min-width: 768px) { .col-md-1 { width: 8.3333333333%; }
  .col-md-2 { width: 16.6666666667%; }
  .col-md-3 { width: 25%; }
  .col-md-4 { width: 33.3333333333%; }
  .col-md-5 { width: 41.6666666667%; }
  .col-md-6 { width: 50%; }
  .col-md-7 { width: 58.3333333333%; }
  .col-md-8 { width: 66.6666666667%; }
  .col-md-9 { width: 75%; }
  .col-md-10 { width: 83.3333333333%; }
  .col-md-11 { width: 91.6666666667%; }
  .col-md-12 { width: 100%; } }
@media (min-width: 1012px) { .col-lg-1 { width: 8.3333333333%; }
  .col-lg-2 { width: 16.6666666667%; }
  .col-lg-3 { width: 25%; }
  .col-lg-4 { width: 33.3333333333%; }
  .col-lg-5 { width: 41.6666666667%; }
  .col-lg-6 { width: 50%; }
  .col-lg-7 { width: 58.3333333333%; }
  .col-lg-8 { width: 66.6666666667%; }
  .col-lg-9 { width: 75%; }
  .col-lg-10 { width: 83.3333333333%; }
  .col-lg-11 { width: 91.6666666667%; }
  .col-lg-12 { width: 100%; } }
@media (min-width: 1280px) { .col-xl-1 { width: 8.3333333333%; }
  .col-xl-2 { width: 16.6666666667%; }
  .col-xl-3 { width: 25%; }
  .col-xl-4 { width: 33.3333333333%; }
  .col-xl-5 { width: 41.6666666667%; }
  .col-xl-6 { width: 50%; }
  .col-xl-7 { width: 58.3333333333%; }
  .col-xl-8 { width: 66.6666666667%; }
  .col-xl-9 { width: 75%; }
  .col-xl-10 { width: 83.3333333333%; }
  .col-xl-11 { width: 91.6666666667%; }
  .col-xl-12 { width: 100%; } }
.gutter { margin-right: -16px; margin-left: -16px; }
.gutter > [class*="col-"] { padding-right: 16px !important; padding-left: 16px !important; }

.gutter-condensed { margin-right: -8px; margin-left: -8px; }
.gutter-condensed > [class*="col-"] { padding-right: 8px !important; padding-left: 8px !important; }

.gutter-spacious { margin-right: -24px; margin-left: -24px; }
.gutter-spacious > [class*="col-"] { padding-right: 24px !important; padding-left: 24px !important; }

@media (min-width: 544px) { .gutter-sm { margin-right: -16px; margin-left: -16px; }
  .gutter-sm > [class*="col-"] { padding-right: 16px !important; padding-left: 16px !important; }
  .gutter-sm-condensed { margin-right: -8px; margin-left: -8px; }
  .gutter-sm-condensed > [class*="col-"] { padding-right: 8px !important; padding-left: 8px !important; }
  .gutter-sm-spacious { margin-right: -24px; margin-left: -24px; }
  .gutter-sm-spacious > [class*="col-"] { padding-right: 24px !important; padding-left: 24px !important; } }
@media (min-width: 768px) { .gutter-md { margin-right: -16px; margin-left: -16px; }
  .gutter-md > [class*="col-"] { padding-right: 16px !important; padding-left: 16px !important; }
  .gutter-md-condensed { margin-right: -8px; margin-left: -8px; }
  .gutter-md-condensed > [class*="col-"] { padding-right: 8px !important; padding-left: 8px !important; }
  .gutter-md-spacious { margin-right: -24px; margin-left: -24px; }
  .gutter-md-spacious > [class*="col-"] { padding-right: 24px !important; padding-left: 24px !important; } }
@media (min-width: 1012px) { .gutter-lg { margin-right: -16px; margin-left: -16px; }
  .gutter-lg > [class*="col-"] { padding-right: 16px !important; padding-left: 16px !important; }
  .gutter-lg-condensed { margin-right: -8px; margin-left: -8px; }
  .gutter-lg-condensed > [class*="col-"] { padding-right: 8px !important; padding-left: 8px !important; }
  .gutter-lg-spacious { margin-right: -24px; margin-left: -24px; }
  .gutter-lg-spacious > [class*="col-"] { padding-right: 24px !important; padding-left: 24px !important; } }
@media (min-width: 1280px) { .gutter-xl { margin-right: -16px; margin-left: -16px; }
  .gutter-xl > [class*="col-"] { padding-right: 16px !important; padding-left: 16px !important; }
  .gutter-xl-condensed { margin-right: -8px; margin-left: -8px; }
  .gutter-xl-condensed > [class*="col-"] { padding-right: 8px !important; padding-left: 8px !important; }
  .gutter-xl-spacious { margin-right: -24px; margin-left: -24px; }
  .gutter-xl-spacious > [class*="col-"] { padding-right: 24px !important; padding-left: 24px !important; } }
.offset-1 { margin-left: 8.3333333333% !important; }

.offset-2 { margin-left: 16.6666666667% !important; }

.offset-3 { margin-left: 25% !important; }

.offset-4 { margin-left: 33.3333333333% !important; }

.offset-5 { margin-left: 41.6666666667% !important; }

.offset-6 { margin-left: 50% !important; }

.offset-7 { margin-left: 58.3333333333% !important; }

.offset-8 { margin-left: 66.6666666667% !important; }

.offset-9 { margin-left: 75% !important; }

.offset-10 { margin-left: 83.3333333333% !important; }

.offset-11 { margin-left: 91.6666666667% !important; }

@media (min-width: 544px) { .offset-sm-1 { margin-left: 8.3333333333% !important; }
  .offset-sm-2 { margin-left: 16.6666666667% !important; }
  .offset-sm-3 { margin-left: 25% !important; }
  .offset-sm-4 { margin-left: 33.3333333333% !important; }
  .offset-sm-5 { margin-left: 41.6666666667% !important; }
  .offset-sm-6 { margin-left: 50% !important; }
  .offset-sm-7 { margin-left: 58.3333333333% !important; }
  .offset-sm-8 { margin-left: 66.6666666667% !important; }
  .offset-sm-9 { margin-left: 75% !important; }
  .offset-sm-10 { margin-left: 83.3333333333% !important; }
  .offset-sm-11 { margin-left: 91.6666666667% !important; } }
@media (min-width: 768px) { .offset-md-1 { margin-left: 8.3333333333% !important; }
  .offset-md-2 { margin-left: 16.6666666667% !important; }
  .offset-md-3 { margin-left: 25% !important; }
  .offset-md-4 { margin-left: 33.3333333333% !important; }
  .offset-md-5 { margin-left: 41.6666666667% !important; }
  .offset-md-6 { margin-left: 50% !important; }
  .offset-md-7 { margin-left: 58.3333333333% !important; }
  .offset-md-8 { margin-left: 66.6666666667% !important; }
  .offset-md-9 { margin-left: 75% !important; }
  .offset-md-10 { margin-left: 83.3333333333% !important; }
  .offset-md-11 { margin-left: 91.6666666667% !important; } }
@media (min-width: 1012px) { .offset-lg-1 { margin-left: 8.3333333333% !important; }
  .offset-lg-2 { margin-left: 16.6666666667% !important; }
  .offset-lg-3 { margin-left: 25% !important; }
  .offset-lg-4 { margin-left: 33.3333333333% !important; }
  .offset-lg-5 { margin-left: 41.6666666667% !important; }
  .offset-lg-6 { margin-left: 50% !important; }
  .offset-lg-7 { margin-left: 58.3333333333% !important; }
  .offset-lg-8 { margin-left: 66.6666666667% !important; }
  .offset-lg-9 { margin-left: 75% !important; }
  .offset-lg-10 { margin-left: 83.3333333333% !important; }
  .offset-lg-11 { margin-left: 91.6666666667% !important; } }
@media (min-width: 1280px) { .offset-xl-1 { margin-left: 8.3333333333% !important; }
  .offset-xl-2 { margin-left: 16.6666666667% !important; }
  .offset-xl-3 { margin-left: 25% !important; }
  .offset-xl-4 { margin-left: 33.3333333333% !important; }
  .offset-xl-5 { margin-left: 41.6666666667% !important; }
  .offset-xl-6 { margin-left: 50% !important; }
  .offset-xl-7 { margin-left: 58.3333333333% !important; }
  .offset-xl-8 { margin-left: 66.6666666667% !important; }
  .offset-xl-9 { margin-left: 75% !important; }
  .offset-xl-10 { margin-left: 83.3333333333% !important; }
  .offset-xl-11 { margin-left: 91.6666666667% !important; } }
.markdown-body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; font-size: 16px; line-height: 1.5; word-wrap: break-word; }
.markdown-body::before { display: table; content: ""; }
.markdown-body::after { display: table; clear: both; content: ""; }
.markdown-body > *:first-child { margin-top: 0 !important; }
.markdown-body > *:last-child { margin-bottom: 0 !important; }
.markdown-body a:not([href]) { color: inherit; text-decoration: none; }
.markdown-body .absent { color: #cb2431; }
.markdown-body .anchor { float: left; padding-right: 4px; margin-left: -20px; line-height: 1; }
.markdown-body .anchor:focus { outline: none; }
.markdown-body p, .markdown-body blockquote, .markdown-body ul, .markdown-body ol, .markdown-body dl, .markdown-body table, .markdown-body pre { margin-top: 0; margin-bottom: 16px; }
.markdown-body hr { height: 0.25em; padding: 0; margin: 24px 0; background-color: #e1e4e8; border: 0; }
.markdown-body blockquote { padding: 0 1em; color: #6a737d; border-left: 0.25em solid #dfe2e5; }
.markdown-body blockquote > :first-child { margin-top: 0; }
.markdown-body blockquote > :last-child { margin-bottom: 0; }
.markdown-body kbd { display: inline-block; padding: 3px 5px; font-size: 11px; line-height: 10px; color: #444d56; vertical-align: middle; background-color: #fafbfc; border: solid 1px #c6cbd1; border-bottom-color: #959da5; border-radius: 3px; box-shadow: inset 0 -1px 0 #959da5; }

.markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6 { margin-top: 24px; margin-bottom: 16px; font-weight: 600; line-height: 1.25; }
.markdown-body h1 .octicon-link, .markdown-body h2 .octicon-link, .markdown-body h3 .octicon-link, .markdown-body h4 .octicon-link, .markdown-body h5 .octicon-link, .markdown-body h6 .octicon-link { color: #1b1f23; vertical-align: middle; visibility: hidden; }
.markdown-body h1:hover .anchor, .markdown-body h2:hover .anchor, .markdown-body h3:hover .anchor, .markdown-body h4:hover .anchor, .markdown-body h5:hover .anchor, .markdown-body h6:hover .anchor { text-decoration: none; }
.markdown-body h1:hover .anchor .octicon-link, .markdown-body h2:hover .anchor .octicon-link, .markdown-body h3:hover .anchor .octicon-link, .markdown-body h4:hover .anchor .octicon-link, .markdown-body h5:hover .anchor .octicon-link, .markdown-body h6:hover .anchor .octicon-link { visibility: visible; }
.markdown-body h1 tt, .markdown-body h1 code, .markdown-body h2 tt, .markdown-body h2 code, .markdown-body h3 tt, .markdown-body h3 code, .markdown-body h4 tt, .markdown-body h4 code, .markdown-body h5 tt, .markdown-body h5 code, .markdown-body h6 tt, .markdown-body h6 code { font-size: inherit; }
.markdown-body h1 { padding-bottom: 0.3em; font-size: 2em; border-bottom: 1px solid #eaecef; }
.markdown-body h2 { padding-bottom: 0.3em; font-size: 1.5em; border-bottom: 1px solid #eaecef; }
.markdown-body h3 { font-size: 1.25em; }
.markdown-body h4 { font-size: 1em; }
.markdown-body h5 { font-size: 0.875em; }
.markdown-body h6 { font-size: 0.85em; color: #6a737d; }

.markdown-body ul, .markdown-body ol { padding-left: 2em; }
.markdown-body ul.no-list, .markdown-body ol.no-list { padding: 0; list-style-type: none; }
.markdown-body ul ul, .markdown-body ul ol, .markdown-body ol ol, .markdown-body ol ul { margin-top: 0; margin-bottom: 0; }
.markdown-body li { word-wrap: break-all; }
.markdown-body li > p { margin-top: 16px; }
.markdown-body li + li { margin-top: 0.25em; }
.markdown-body dl { padding: 0; }
.markdown-body dl dt { padding: 0; margin-top: 16px; font-size: 1em; font-style: italic; font-weight: 600; }
.markdown-body dl dd { padding: 0 16px; margin-bottom: 16px; }

.markdown-body table { display: block; width: 100%; overflow: auto; }
.markdown-body table th { font-weight: 600; }
.markdown-body table th, .markdown-body table td { padding: 6px 13px; border: 1px solid #dfe2e5; }
.markdown-body table tr { background-color: #fff; border-top: 1px solid #c6cbd1; }
.markdown-body table tr:nth-child(2n) { background-color: #f6f8fa; }
.markdown-body table img { background-color: transparent; }

.markdown-body img { max-width: 100%; box-sizing: content-box; background-color: #fff; }
.markdown-body img[align=right] { padding-left: 20px; }
.markdown-body img[align=left] { padding-right: 20px; }
.markdown-body .emoji { max-width: none; vertical-align: text-top; background-color: transparent; }
.markdown-body span.frame { display: block; overflow: hidden; }
.markdown-body span.frame > span { display: block; float: left; width: auto; padding: 7px; margin: 13px 0 0; overflow: hidden; border: 1px solid #dfe2e5; }
.markdown-body span.frame span img { display: block; float: left; }
.markdown-body span.frame span span { display: block; padding: 5px 0 0; clear: both; color: #24292e; }
.markdown-body span.align-center { display: block; overflow: hidden; clear: both; }
.markdown-body span.align-center > span { display: block; margin: 13px auto 0; overflow: hidden; text-align: center; }
.markdown-body span.align-center span img { margin: 0 auto; text-align: center; }
.markdown-body span.align-right { display: block; overflow: hidden; clear: both; }
.markdown-body span.align-right > span { display: block; margin: 13px 0 0; overflow: hidden; text-align: right; }
.markdown-body span.align-right span img { margin: 0; text-align: right; }
.markdown-body span.float-left { display: block; float: left; margin-right: 13px; overflow: hidden; }
.markdown-body span.float-left span { margin: 13px 0 0; }
.markdown-body span.float-right { display: block; float: right; margin-left: 13px; overflow: hidden; }
.markdown-body span.float-right > span { display: block; margin: 13px auto 0; overflow: hidden; text-align: right; }

.markdown-body code, .markdown-body tt { padding: 0.2em 0.4em; margin: 0; font-size: 85%; background-color: rgba(27, 31, 35, 0.05); border-radius: 3px; }
.markdown-body code br, .markdown-body tt br { display: none; }
.markdown-body del code { text-decoration: inherit; }
.markdown-body pre { word-wrap: normal; }
.markdown-body pre > code { padding: 0; margin: 0; font-size: 100%; word-break: normal; white-space: pre; background: transparent; border: 0; }
.markdown-body .highlight { margin-bottom: 16px; }
.markdown-body .highlight pre { margin-bottom: 0; word-break: normal; }
.markdown-body .highlight pre, .markdown-body pre { padding: 16px; overflow: auto; font-size: 85%; line-height: 1.45; background-color: #f6f8fa; border-radius: 3px; }
.markdown-body pre code, .markdown-body pre tt { display: inline; max-width: auto; padding: 0; margin: 0; overflow: visible; line-height: inherit; word-wrap: normal; background-color: transparent; border: 0; }

.markdown-body .csv-data td, .markdown-body .csv-data th { padding: 5px; overflow: hidden; font-size: 12px; line-height: 1; text-align: left; white-space: nowrap; }
.markdown-body .csv-data .blob-num { padding: 10px 8px 9px; text-align: right; background: #fff; border: 0; }
.markdown-body .csv-data tr { border-top: 0; }
.markdown-body .csv-data th { font-weight: 600; background: #f6f8fa; border-top: 0; }

.highlight table td { padding: 5px; }

.highlight table pre { margin: 0; }

.highlight .cm { color: #999988; font-style: italic; }

.highlight .cp { color: #999999; font-weight: bold; }

.highlight .c1 { color: #999988; font-style: italic; }

.highlight .cs { color: #999999; font-weight: bold; font-style: italic; }

.highlight .c, .highlight .cd { color: #999988; font-style: italic; }

.highlight .err { color: #a61717; background-color: #e3d2d2; }

.highlight .gd { color: #000000; background-color: #ffdddd; }

.highlight .ge { color: #000000; font-style: italic; }

.highlight .gr { color: #aa0000; }

.highlight .gh { color: #999999; }

.highlight .gi { color: #000000; background-color: #ddffdd; }

.highlight .go { color: #888888; }

.highlight .gp { color: #555555; }

.highlight .gs { font-weight: bold; }

.highlight .gu { color: #aaaaaa; }

.highlight .gt { color: #aa0000; }

.highlight .kc { color: #000000; font-weight: bold; }

.highlight .kd { color: #000000; font-weight: bold; }

.highlight .kn { color: #000000; font-weight: bold; }

.highlight .kp { color: #000000; font-weight: bold; }

.highlight .kr { color: #000000; font-weight: bold; }

.highlight .kt { color: #445588; font-weight: bold; }

.highlight .k, .highlight .kv { color: #000000; font-weight: bold; }

.highlight .mf { color: #009999; }

.highlight .mh { color: #009999; }

.highlight .il { color: #009999; }

.highlight .mi { color: #009999; }

.highlight .mo { color: #009999; }

.highlight .m, .highlight .mb, .highlight .mx { color: #009999; }

.highlight .sb { color: #d14; }

.highlight .sc { color: #d14; }

.highlight .sd { color: #d14; }

.highlight .s2 { color: #d14; }

.highlight .se { color: #d14; }

.highlight .sh { color: #d14; }

.highlight .si { color: #d14; }

.highlight .sx { color: #d14; }

.highlight .sr { color: #009926; }

.highlight .s1 { color: #d14; }

.highlight .ss { color: #990073; }

.highlight .s { color: #d14; }

.highlight .na { color: #008080; }

.highlight .bp { color: #999999; }

.highlight .nb { color: #0086B3; }

.highlight .nc { color: #445588; font-weight: bold; }

.highlight .no { color: #008080; }

.highlight .nd { color: #3c5d5d; font-weight: bold; }

.highlight .ni { color: #800080; }

.highlight .ne { color: #990000; font-weight: bold; }

.highlight .nf { color: #990000; font-weight: bold; }

.highlight .nl { color: #990000; font-weight: bold; }

.highlight .nn { color: #555555; }

.highlight .nt { color: #000080; }

.highlight .vc { color: #008080; }

.highlight .vg { color: #008080; }

.highlight .vi { color: #008080; }

.highlight .nv { color: #008080; }

.highlight .ow { color: #000000; font-weight: bold; }

.highlight .o { color: #000000; font-weight: bold; }

.highlight .w { color: #bbbbbb; }

.highlight { background-color: #f8f8f8; }
