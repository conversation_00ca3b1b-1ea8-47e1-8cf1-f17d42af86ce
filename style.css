:root {
    --bg-color-light: #0a0a0a;
    --bg-color-dark: #000000;
    --text-color-light: #ff0000;
    --text-color-dark: #ff3333;
    --header-height: 145px;
    --footer-height: 145px;
    --sidebar-width: 300px;
    --accent-color: #ff0000;
    --accent-color-hover: #cc0000;
    --tech-badge-bg: #1a0000;
    --tech-badge-text: #ff6666;
    --tech-badge-bg-dark: #330000;
    --tech-badge-text-dark: #ff9999;
    --background-color: var(--bg-color-light);
    --background-color-dark: var(--bg-color-dark);
    --border-color: #ff0000;
    --border-color-dark: #990000;
}

body {
    background-color: var(--bg-color-light);
    color: var(--text-color-light);
    margin: 0;
    padding: 0;
    overflow-y: hidden;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 0, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 0, 0.05) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 0, 0, 0.02) 50%, transparent 70%);
    animation: hackingGlow 3s ease-in-out infinite alternate;
}

@keyframes hackingGlow {
    0% { box-shadow: inset 0 0 50px rgba(255, 0, 0, 0.1); }
    100% { box-shadow: inset 0 0 100px rgba(255, 0, 0, 0.2); }
}

body.vibe {
    font-family: 'Courier New', 'SF Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: hidden;
    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
}

@media (max-width: 768px) {
    body {
        overflow-y: auto !important;
    }
}

body.dark-mode {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 0, 0, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 0, 0.08) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 0, 0, 0.03) 50%, transparent 70%);
}

.dark-mode-toggle {
    background-color: transparent;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.dark-mode-toggle:hover {
    opacity: 1;
}

.mode-icon {
    width: 20px;
    height: 20px;
    stroke: currentColor;
}

.site-header {
    padding: 1rem 2rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-color-light);
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--header-height);
    box-shadow: 0 2px 10px rgba(255, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.dark-mode .site-header {
    background-color: var(--bg-color-dark);
    border-bottom-color: var(--border-color-dark);
    box-shadow: 0 2px 15px rgba(255, 0, 0, 0.4);
}

.header-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 0;
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.star-count {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--accent-color);
    font-size: 0.9rem;
    opacity: 0.8;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.star-count:hover {
    opacity: 1;
}

.star-count svg {
    width: 16px;
    height: 16px;
}

.social-links {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-right: 8px;
}

.social-link {
    color: var(--accent-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-link:hover {
    opacity: 1;
}

.social-link svg {
    width: 24px;
    height: 24px;
}

.techstack-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-right: 0.5rem;
}

.tech-badge {
    background: var(--tech-badge-bg);
    color: var(--tech-badge-text);
    padding: 0.1rem 0.2rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    white-space: nowrap;
}

.dark-mode .tech-badge {
    background: var(--tech-badge-bg-dark);
    color: var(--tech-badge-text-dark);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 12px;
    left: 12px;
    right: 0;
}

.card-footer .contributor-badge {
    bottom: 0;
}

.site-footer {
    padding: 12px 20px;
    text-align: center;
    border-top: 1px solid var(--border-color);
    background: var(--bg-color-light);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    height: var(--footer-height);
    box-shadow: 0 -2px 10px rgba(255, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.dark-mode .site-footer {
    background: var(--bg-color-dark);
    border-color: var(--border-color-dark);
    box-shadow: 0 -2px 15px rgba(255, 0, 0, 0.4);
}

.footer-content {
    max-width: none;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0 30px;
    text-align: left;
    font-size: 0.7rem;
    padding: 0 1rem;
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.footer-section h3 {
    font-size: 0.8rem;
    margin: 0;
    opacity: 0.8;
}

.dark-mode .footer-section h3 {
    color: var(--text-color-dark);
}

.footer-section p {
    font-size: inherit;
    margin: 0;
    line-height: 1.3;
    opacity: 0.6;
    color: var(--text-color-light);
}

.dark-mode .footer-section p {
    color: var(--text-color-dark);
}

.book-links {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.book-link svg {
    width: 14px;
    height: 14px;
}

/* Add padding to main content to prevent overlap with fixed footer */
.layout-wrapper {
    padding-bottom: 100px;
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 12px;
        text-align: left;
        padding: 0 1rem;
    }
    
    .footer-section {
        text-align: left;
    }
    
    .footer-section p {
        text-align: left;
    }
    
    .book-links {
        align-items: flex-start;
    }
    
    .site-footer {
        position: static;
        text-align: left;
    }
    
    .layout-wrapper {
        padding-bottom: 0;
    }
    
    .site-description {
        display: none !important;
    }
    
    .site-header {
        padding: 0.75rem 1rem;
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        position: relative;
    }

    body.vibe .site-header {
        height: auto !important;
    }

    body.vibe .sidebar {
        display: none !important;
    }

    .header-right {
        position: absolute;
        top: 1.3rem;
        right: 0.75rem;
        gap: 8px;
        display: flex;
        align-items: center;
    }
    
    .star-count {
        display: flex;
        font-size: 0.8rem;
    }
    
    .star-count svg {
        width: 14px;
        height: 14px;
    }
    
    .dark-mode-toggle {
        padding: 4px;
    }
    
    .mode-icon {
        width: 18px;
        height: 18px;
    }
    
    .site-slogan {
        display: none;
    }
    
    .site-description {
        display: flex !important;
        margin-top: 4px;
    }
    
    .platform-hint {
        font-size: 0.7rem;
    }
    
    .platform-tag {
        padding: 1px 6px;
        font-size: 0.75rem;
    }
    
    .github-link {
        display: none;
    }
    
    .github-link span {
        display: none;
    }
    
    .search-container {
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
    }
}

.site-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(45deg, var(--accent-color), #ff6666);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
    position: relative;
    transition: transform 0.3s ease;
    text-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
}

/* Blinking cursor animation for development mode */
@keyframes blink-cursor {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

/* Hacking matrix effect */
@keyframes matrix-rain {
    0% { transform: translateY(-100vh); opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

.matrix-bg::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 0, 0, 0.03) 100px
    );
    pointer-events: none;
    z-index: -1;
}

.site-slogan {
    font-size: 0.9rem;
    opacity: 0.7;
    margin: 0 0 0.8rem 0;
    line-height: 1;
}

@media (max-width: 768px) {
    .site-slogan {
        display: none;
    }
    
    .site-description {
        display: flex !important;
        margin-top: 4px;
    }
    
    .platform-hint {
        font-size: 0.7rem;
    }
    
    .platform-tag {
        padding: 1px 6px;
        font-size: 0.75rem;
    }
    
    .header-right {
        gap: 8px;
    }
    
    .mode-text {
        display: none;
    }
    
    .dark-mode-toggle {
        padding: 6px;
    }
    
    .github-link {
        padding: 6px;
    }
    
    .github-link span {
        display: none;
    }
}

.site-title:hover {
    transform: translateY(-2px);
}

.site-title::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--accent-color), #ff6666);
    filter: blur(20px);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.site-title:hover::before {
    opacity: 0.3;
}

.site-title::after {
    content: '█';
    animation: blink-cursor 1s infinite;
    color: var(--accent-color);
    margin-left: 5px;
}

.site-description {
    color: var(--text-color-light);
    margin: 0;
    font-size: 0.95rem;
    opacity: 0.8;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.platform-hint {
    font-size: 0.75rem;
    opacity: 0.6;
    margin: 0;
}

.platform-pills {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.platform-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.platform-tag:hover {
    background: rgba(16, 185, 129, 0.2);
    transform: translateY(-1px);
}

.platform-tag.active {
    background: var(--accent-color);
    color: white;
    transform: translateY(-1px);
}

.dark-mode .platform-tag {
    background: rgba(16, 185, 129, 0.2);
}

.dark-mode .platform-tag:hover {
    background: rgba(16, 185, 129, 0.25);
}

.dark-mode .platform-tag.active {
    background: var(--accent-color);
}

.dark-mode .site-description {
    color: var(--text-color-dark);
}

.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.chat-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 2px;
    color: var(--accent-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
    z-index: 2;
    flex-shrink: 0;
}

.chat-button:hover {
    opacity: 1;
}

.chat-button svg {
    width: 16px;
    height: 16px;
}

.yaml-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 2px;
    color: var(--accent-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
    z-index: 2;
    flex-shrink: 0;
}

.yaml-button:hover {
    opacity: 1;
}

.yaml-button svg {
    width: 16px;
    height: 16px;
}

.create-yaml-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.create-yaml-button:hover {
    background-color: #0e9f71;
}

.create-yaml-button svg {
    width: 14px;
    height: 14px;
}

.dark-mode .create-yaml-button {
    background-color: var(--accent-color);
    color: white;
}

.dark-mode .create-yaml-button:hover {
    background-color: #0e9f71;
}

.yaml-content {
    background-color: #f5f5f5;
    padding: 12px;
    border-radius: 6px;
    color: #333;
    font-family: "SF Mono", "Segoe UI Mono", "Roboto Mono", monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow: auto;
    white-space: pre-wrap;
    margin: 0;
}

.dark-mode .yaml-content {
    background-color: #2d2d2d;
    color: #f5f5f5;
}

/* YAML syntax highlighting */
.yaml-content .key {
    color: #0550ae;
    font-weight: 500;
}

.dark-mode .yaml-content .key {
    color: #4d9eee;
}

.yaml-content .string {
    color: #22863a;
}

.yaml-content .string .number {
    color: #56b366 !important;
}

.dark-mode .yaml-content .string {
    color: #56b366;
}

.yaml-content .number {
    color: #905;
}

.dark-mode .yaml-content .number {
    color: #e76086;
}

.yaml-content .boolean {
    color: #0550ae;
    font-weight: 500;
}

.dark-mode .yaml-content .boolean {
    color: #4d9eee;
}

/* Style title as code in the yml modal */
#yamlModalOverlay .modal-title {
    font-family: "SF Mono", "Segoe UI Mono", "Roboto Mono", monospace;
    font-size: 1.2rem;
}

.layout-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.content-wrapper {
    display: flex;
    flex: 1;
}

.sidebar {
    width: var(--sidebar-width);
    padding: 20px;
    border-right: 1px solid #e1e4e8;
    height: calc(100vh - var(--header-height) - var(--footer-height));
    overflow-y: auto;
    background-color: var(--bg-color-light);
}

.dark-mode .sidebar {
    background-color: var(--bg-color-dark);
    border-right-color: #2d2d2d;
    scrollbar-color: #2d2d2d transparent;
}

.main-content {
    flex: 1;
    height: calc(100vh - var(--header-height) - var(--footer-height));
    overflow-y: auto;
    padding: 20px;
    padding-top: 0;
    position: relative;
}

.dark-mode .main-content {
    scrollbar-color: #2d2d2d transparent;
}

.main-content-header {
    position: sticky;
    top: 0;
    background-color: var(--bg-color-light);
    padding: 6px 38px;
    border-bottom: 1px solid #e1e4e8;
    z-index: 10;
    margin: -20px -20px 20px -20px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.dark-mode .main-content-header {
    background-color: var(--bg-color-dark);
    border-color: #2d2d2d;
}

.main-content-header .header-content {
    display: flex;
    align-items: center;
    justify-content: end;
    margin: 0 auto;
    gap: 16px;
}

.main-content-header .header-description {
    font-size: 0.85rem;
    color: var(--text-color-light);
    opacity: 0.8;
    white-space: nowrap;
}

.dark-mode .main-content-header .header-description {
    color: var(--text-color-dark);
}

.main-content-header .platform-selectors {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: var(--text-color-light);
    opacity: 0.9;
    flex-wrap: wrap;
    margin-right: 0 !important;
}

.dark-mode .main-content-header .platform-selectors {
    color: var(--text-color-dark);
}

.main-content-header .dev-mode-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    background-color: var(--bg-color-light);
    min-width: 120px;
    margin-left: auto;
}

.dark-mode .main-content-header .dev-mode-toggle {
    background-color: var(--bg-color-dark);
    border-color: #2d2d2d;
}

.main-content-header .dev-mode-toggle:hover {
    border-color: var(--accent-color);
}

.main-content-header .dev-mode-label {
    font-size: 13px;
    color: var(--text-color-light);
    white-space: nowrap;
    order: 1;
}

.dark-mode .main-content-header .dev-mode-label {
    color: var(--text-color-dark);
}

.main-content-header .switch {
    order: 2;
}

@media (max-width: 768px) {
    .main-content-header {
        padding: 12px 40px;
        margin: -16px -16px 16px -16px;
    }
    
    .main-content-header .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .main-content-header .header-description {
        text-align: left;
        white-space: normal;
    }
    
    .main-content-header .platform-selectors {
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;
        align-items: center;
        text-align: left;
        gap: 4px;
    }
    
    .main-content-header .custom-select,
    .main-content-header .custom-input {
        width: auto;
        min-width: 0;
        flex: 0 1 auto;
    }
    
    .main-content-header .custom-input {
        width: 100%;
    }
    
    /* Show custom inputs on new line when visible */
    .main-content-header .custom-input:not([style*="display: none"]) {
        margin-top: 4px;
        margin-bottom: 4px;
    }
}

.main-content-header .custom-select {
    padding: 2px 20px 2px 4px;
    font-size: inherit;
    border: none;
    border-radius: 4px;
    background-color: transparent;
    color: var(--accent-color);
    cursor: pointer;
    min-width: 0;
    font-weight: 500;
    background-position: right 4px center;
    background-size: 12px;
    text-decoration: underline;
    text-underline-offset: 2px;
}

.main-content-header .custom-select:hover {
    opacity: 0.8;
}

.main-content-header .custom-input {
    padding: 2px 4px;
    font-size: inherit;
    border: none;
    border-bottom: 1px dashed var(--accent-color);
    border-radius: 0;
    background-color: transparent;
    color: var(--accent-color);
    min-width: 100px;
    font-weight: 500;
}

.main-content-header .custom-input:focus {
    outline: none;
    box-shadow: none;
    border-bottom-style: solid;
}

.prompts-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    max-width: 100%;
    margin: 0;
    padding: 16px;
}

.container-lg.markdown-body {
    padding: 0;
    max-width: none;
}

@media (max-width: 1600px) {
    .prompts-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) {
    .prompts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .prompts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .contributor-badge {
        margin-left: 0;
    }
}

.prompt-card {
    background: var(--bg-color-light);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.2);
    backdrop-filter: blur(5px);
}

.dark-mode .prompt-card {
    background: var(--bg-color-dark);
    border-color: var(--border-color-dark);
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
}

.prompt-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(255, 0, 0, 0.4);
    border-color: var(--accent-color);
    background: rgba(255, 0, 0, 0.05);
}

.prompt-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 12px 0;
    color: var(--text-color-light);
    line-height: 1.4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark-mode .prompt-title {
    color: var(--text-color-dark);
}

.prompt-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color-light);
    opacity: 0.8;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 40px;
}

.dark-mode .prompt-content {
    color: var(--text-color-dark);
}

.copy-button {
    position: static;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 2px;
    color: var(--accent-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
    z-index: 2;
    margin-left: 8px;
    flex-shrink: 0;
}

.copy-button:hover {
    opacity: 1;
}

.copy-button svg {
    width: 16px;
    height: 16px;
}

.search-container {
    margin-bottom: 20px;
}

.prompt-count {
    margin-bottom: 12px;
    padding: 6px 10px;
    border-radius: 6px;
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-color);
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dark-mode .prompt-count {
    background: rgba(16, 185, 129, 0.15);
}

.prompt-count .count-number {
    font-weight: 600;
    font-size: 0.9rem;
}

.prompt-count.filtered {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.dark-mode .prompt-count.filtered {
    background: rgba(59, 130, 246, 0.15);
}

#searchInput {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: var(--bg-color-light);
    color: var(--text-color-light);
    font-family: 'Courier New', monospace;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

#searchInput::placeholder {
    color: rgba(255, 0, 0, 0.6);
}

#searchInput:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.5);
}

.dark-mode #searchInput {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
    border-color: var(--border-color-dark);
}

#searchResults {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-result-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.search-result-item:hover {
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 6px;
    transform: translateX(4px);
}

.dark-mode .search-result-item:hover {
    background-color: rgba(16, 185, 129, 0.2);
}

.search-result-item.active {
    background-color: var(--accent-color);
    color: white;
}

@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e1e4e8;
    }
    
    .dark-mode .sidebar {
        border-bottom-color: #2d2d2d;
    }
    
    .main-content {
        height: auto;
    }
    
    .header-right {
        gap: 8px;
    }
    
    .mode-text {
        display: none;
    }
    
    .dark-mode-toggle {
        padding: 6px;
    }
    
    .github-link {
        padding: 6px;
    }
    
    .github-link span {
        display: none;
    }
}

/* Add modal styles */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-color-light);
    border-radius: 12px;
    padding: 24px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 1001;
    box-shadow: 0 0 30px rgba(255, 0, 0, 0.5);
    border: 1px solid var(--accent-color);
    backdrop-filter: blur(10px);
}

.dark-mode .modal {
    background: var(--bg-color-dark);
    box-shadow: 0 0 40px rgba(255, 0, 0, 0.7);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.modal-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
    padding-right: 80px;
    color: var(--text-color-light);
    flex: 1;
}

.modal-copy-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: var(--accent-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close {
    position: static;
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--text-color-light);
    padding: 4px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode .modal-close {
    color: var(--text-color-dark);
}

.modal-close:hover {
    opacity: 1;
}

.prompt-card {
    cursor: pointer;
}

/* Add contributor badge styles */
.contributor-badge {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 0.65rem;
    color: var(--accent-color);
    text-decoration: none;
    opacity: 0.7;
    transition: all 0.2s ease;
    background: rgba(16, 185, 129, 0.1);
    padding: 1px 6px;
    border-radius: 8px;
}

.dark-mode .contributor-badge {
    background: rgba(16, 185, 129, 0.15);
}

.contributor-badge:hover {
    opacity: 1;
    transform: translateY(-1px);
    background: var(--accent-color);
    color: white;
}

.dark-mode .contributor-badge:hover {
    color: var(--bg-color-dark);
}

.prompt-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color-light);
    opacity: 0.8;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 40px;
}

/* Add modal header and footer styles */
.modal-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e1e4e8;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.modal-footer-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 0;
    max-width: 50%;
}

.modal-footer-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0 0 auto;
    white-space: nowrap;
}

.modal-chat-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    background: var(--accent-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-chat-button:hover {
    background: var(--accent-color-hover);
}

.modal-chat-button svg {
    width: 16px;
    height: 16px;
}

.modal-contributor {
    font-size: 0.8rem;
    color: var(--accent-color);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.modal-contributor:hover {
    opacity: 1;
}

.dark-mode .modal-title {
    color: var(--text-color-dark);
}

.modal-content {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color-light);
}

.dark-mode .modal-content {
    color: var(--text-color-dark);
}

.modal-copy-button:hover {
    opacity: 1;
}

.modal-copy-button svg {
    width: 20px;
    height: 20px;
}

.modal-close svg {
    width: 20px;
    height: 20px;
}

.dark-mode .modal-close {
    color: var(--text-color-dark);
}

.footer-section a {
    color: var(--accent-color);
    text-decoration: none;
}

.footer-section a:hover {
    opacity: 1;
}

.book-link {
    color: var(--accent-color) !important;
    padding: 1px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.dark-mode .book-link {
    color: var(--accent-color) !important;
}

.book-link:hover {
    opacity: 1;
}

.social-footer-link {
    color: var(--accent-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.dark-mode .social-footer-link {
    color: var(--accent-color);
}

.contribute-card {
    border: 2px dashed var(--accent-color);
    background: rgba(16, 185, 129, 0.05);
    transition: all 0.2s ease;
}

.contribute-card:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateY(-2px);
}

.dark-mode .contribute-card {
    background: rgba(16, 185, 129, 0.1);
}

.dark-mode .contribute-card:hover {
    background: rgba(16, 185, 129, 0.15);
}

.search-result-item.add-prompt {
    color: var(--accent-color);
    border: 1px dashed var(--accent-color);
    background: rgba(16, 185, 129, 0.05);
    transition: all 0.2s ease;
}

.search-result-item.add-prompt:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateY(-1px);
}

.dark-mode .search-result-item.add-prompt {
    background: rgba(16, 185, 129, 0.1);
}

.dark-mode .search-result-item.add-prompt:hover {
    background: rgba(16, 185, 129, 0.15);
}

.star-count {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--accent-color);
    font-size: 0.9rem;
    opacity: 0.8;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.star-count:hover {
    opacity: 1;
}

.star-count svg {
    width: 16px;
    height: 16px;
}

@media (max-width: 768px) {
    .star-count {
        display: flex;
        font-size: 0.8rem;
    }
    
    .star-count svg {
        width: 14px;
        height: 14px;
    }
}

/* Dev Mode Toggle Switch Styles */
.dev-mode-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 12px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    background-color: var(--bg-color-light);
    min-width: 120px;
    flex: 1;
}

.dev-mode-label {
    font-size: 14px;
    color: var(--text-color-light);
    white-space: nowrap;
    order: 1;
}

.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.dark-mode .slider {
    background-color: #555;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
}

.dark-mode .slider:before {
    background-color: #e1e1e1;
}

input:checked + .slider {
    background-color: var(--accent-color);
}

.dark-mode input:checked + .slider {
    background-color: var(--accent-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--accent-color);
}

.dark-mode input:focus + .slider {
    box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.main-content-header .dev-mode-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 12px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    background-color: var(--bg-color-light);
    min-width: 160px;
    flex: 1;
}

.dark-mode .main-content-header .dev-mode-toggle {
    background-color: var(--bg-color-dark);
    border-color: #2d2d2d;
}

.main-content-header .dev-mode-toggle:hover {
    border-color: var(--accent-color);
}

.main-content-header .dev-mode-label {
    font-size: 14px;
    color: var(--text-color-light);
    white-space: nowrap;
    order: 1;
}

.dark-mode .main-content-header .dev-mode-label {
    color: var(--text-color-dark);
}

.main-content-header .switch {
    order: 2;
}

@media (max-width: 768px) {
    .main-content-header .dev-mode-toggle {
        width: 100%;
        order: -1;
    }
    
    .main-content-header .dev-mode-label {
        display: inline-block;
    }
    
    .main-content-header .platform-selectors {
        flex-direction: column;
        width: 100%;
    }
}

/* Copilot Suggestion Modal Styles */
.copilot-suggestion-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    z-index: 1001;
    display: none;
}

.dark-mode .copilot-suggestion-backdrop {
    background: rgba(0, 0, 0, 0.7);
}

.copilot-suggestion-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-color-light);
    border-radius: 12px;
    padding: 32px;
    width: 90%;
    max-width: 520px;
    z-index: 1002;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
    display: none;
}

.dark-mode .copilot-suggestion-modal {
    background: var(--bg-color-dark);
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.4);
}

.prompts-count-label {
    display: none;
}

.dev-mode .count-label {
    display: none;
}

.dev-mode .prompts-count-label {
    display: block;
}

.copilot-suggestion-content {
    margin-bottom: 24px;
    color: var(--text-color-light);
    font-size: 1.1rem;
    line-height: 1.5;
}

.dark-mode .copilot-suggestion-content {
    color: var(--text-color-dark);
}

.copilot-suggestion-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.copilot-suggestion-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color-light);
    font-size: 0.9rem;
    opacity: 0.8;
    order: 2;
    margin-top: 8px;
}

.dark-mode .copilot-suggestion-checkbox {
    color: var(--text-color-dark);
}

.copilot-suggestion-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color);
    cursor: pointer;
    margin: 0;
}

.copilot-suggestion-checkbox:hover {
    opacity: 1;
}

.copilot-suggestion-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    order: 1;
}

.copilot-suggestion-button {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.95rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    width: 100%;
}

.copilot-suggestion-button.primary {
    background: var(--accent-color);
    color: white;
}

.copilot-suggestion-button.primary:hover {
    background: var(--accent-color-hover);
    transform: translateY(-1px);
}

.copilot-suggestion-button.secondary {
    background: transparent;
    color: var(--text-color-light);
    border: 1px solid #e1e4e8;
}

.dark-mode .copilot-suggestion-button.secondary {
    color: var(--text-color-dark);
    border-color: #2d2d2d;
}

.copilot-suggestion-button.secondary:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.dark-mode .copilot-suggestion-button.secondary:hover {
    background: rgba(255, 255, 255, 0.05);
}

@media (max-width: 480px) {
    .copilot-suggestion-modal {
        padding: 24px;
        max-width: 100%;
        margin: 0 16px;
    }
    
    .copilot-suggestion-content {
        font-size: 1rem;
        margin-bottom: 20px;
    }
    
    .copilot-suggestion-button {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

/* Variable Input Field Styles */
.variable-container {
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(16, 185, 129, 0.05);
    border-radius: 8px;
    border: 1px solid var(--accent-color);
}

.variable-form {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.variable-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.variable-input-wrapper label {
    font-size: 0.8rem;
    color: var(--accent-color);
    font-weight: 500;
}

.variable-input {
    padding: 8px;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    font-size: 0.9rem;
    background: var(--bg-color-light);
    color: var(--text-color-light);
    transition: all 0.2s ease;
}

.dark-mode .variable-input {
    background: var(--bg-color-dark);
    color: var(--text-color-dark);
    border-color: #2d2d2d;
}

.variable-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.modal-content {
    /* white-space: pre-wrap; */
}

.chrome-ext-link {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--accent-color);
    font-size: 0.9rem;
    opacity: 0.8;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.chrome-ext-link:hover {
    opacity: 1;
}

.chrome-ext-link svg {
    width: 20px;
    height: 20px;
}

.platform-selectors {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 16px;
}

.custom-select {
    padding: 6px 12px;
    font-size: 14px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    background-color: var(--bg-color-light);
    color: var(--text-color-light);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
}

.dark-mode .custom-select {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
    border-color: #2d2d2d;
}

.custom-select:hover {
    border-color: var(--accent-color);
}

.custom-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.custom-input {
    padding: 6px 12px;
    font-size: 14px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    background-color: var(--bg-color-light);
    color: var(--text-color-light);
    transition: all 0.2s ease;
    min-width: 150px;
}

.dark-mode .custom-input {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
    border-color: #2d2d2d;
}

.custom-input:hover {
    border-color: var(--accent-color);
}

.custom-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.custom-input::placeholder {
    color: #6a737d;
    opacity: 0.8;
}

.dark-mode .custom-input::placeholder {
    color: #8b949e;
}

@media (max-width: 768px) {
    .platform-selectors {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        margin-bottom: 12px;
        width: 100%;
    }
    
    .custom-select,
    .custom-input {
        width: 100%;
    }
}

.main-content-header .custom-select {
    min-width: 0;
}

.main-content-header .custom-input {
    flex: 1;
    min-width: 0;
}

@media (max-width: 768px) {
    .main-content-header {
        padding: 12px 40px;
        margin: -16px -16px 16px -16px;
    }
    
    .main-content-header .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .main-content-header .header-description {
        text-align: left;
        white-space: normal;
    }
    
    .main-content-header .platform-selectors {
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;
        align-items: center;
        text-align: left;
        gap: 4px;
    }
    
    .main-content-header .custom-select,
    .main-content-header .custom-input {
        width: auto;
        min-width: 0;
        flex: 0 1 auto;
    }
    
    .main-content-header .custom-input {
        width: 100%;
    }
    
    /* Show custom inputs on new line when visible */
    .main-content-header .custom-input:not([style*="display: none"]) {
        margin-top: 4px;
        margin-bottom: 4px;
    }
}

.platform-tag-container {
    position: relative;
    display: inline-block;
}

.grok-mode-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 160px;
    margin-top: 4px;
    background: var(--bg-color-light);
    border: 1px solid var(--accent-color);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 0.8rem;
    overflow: hidden;
    padding: 4px;
}

.dark-mode .grok-mode-dropdown {
    background: var(--bg-color-dark);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.grok-mode-option {
    padding: 4px 8px;
    cursor: pointer;
    color: var(--text-color-light);
    transition: all 0.2s ease;
    border-radius: 4px;
    white-space: nowrap;
}

.dark-mode .grok-mode-option {
    color: var(--text-color-dark);
}

.grok-mode-option:hover {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-color);
}

.dark-mode .grok-mode-option:hover {
    background: rgba(16, 185, 129, 0.2);
}

.cursor-logo {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--accent-color);
    opacity: 0.8;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    margin-right: 8px;
}

.cursor-logo:hover {
    opacity: 1;
    transform: translateY(-1px);
}

.cursor-logo svg {
    width: 16px;
    height: 16px;
}

/* Style for the Cursor AI logo paths in dark mode */
.cursor-logo path[fill="#fff"] {
    transition: fill 0.2s ease;
}

.dark-mode .cursor-logo path[fill="#fff"] {
    fill: #2d2d2d;
}

@media (max-width: 768px) {
    .cursor-logo span {
        display: none;
    }
}

.modal-actions {
    display: flex;
    gap: 0.5rem;
}

.modal-hint {
    margin-bottom: 0;
    font-size: 0.75rem;
    color: var(--text-color-light);
    opacity: 0.7;
    display: block;
    line-height: 1.4;
    max-width: 300px;
}

.dark-mode .modal-hint {
    color: var(--text-color-dark);
}

.content-well {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.dark-mode .content-well {
    background-color: var(--background-color-dark);
    border-color: var(--border-color-dark) !important;
}

.modal-content pre {
    margin: 0;
    padding: 1rem;
    background-color: var(--code-background);
    border-radius: 0;
    overflow-x: auto;
}

.modal-content code {
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--code-text);
    white-space: pre-wrap;
    word-break: break-word;
}

/* Dark mode adjustments */

.dark-mode .modal-content pre {
    background-color: var(--dark-code-background);
}

.dark-mode .modal-content code {
    color: var(--dark-code-text);
}

.dark-mode .modal-hint {
    background-color: var(--dark-hover-color);
}

.dark-mode .content-well {
    border-color: var(--dark-border-color);
    background-color: var(--dark-background);
}

.github-form {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 0;
    justify-content: flex-end;
    flex-wrap: nowrap;
}

.github-inputs {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 13px;
    color: var(--text-color-light);
}

.github-inputs span {
    opacity: 0.7;
    font-size: 12px;
}

.github-input {
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 12px;
    width: 100px;
    background-color: var(--bg-color-light);
    color: var(--text-color-light);
    transition: all 0.2s ease;
}

#github-branch {
    width: 80px;
}

@media (max-width: 768px) {
    .modal-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .modal-footer-left {
        max-width: 100%;
        margin-bottom: 0;
    }
    
    .modal-footer-right {
        width: 100%;
    }
    
    .github-form {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
        width: 100%;
    }
    
    .github-inputs {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 6px;
    }
    
    .github-inputs span {
        margin: 0 2px;
    }
    
    .github-input {
        flex: 1;
        min-width: 60px;
    }
    
    .create-yaml-button {
        width: 100%;
    }
}

.dark-mode .modal-footer {
    border-color: #2d2d2d;
}

.github-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.dark-mode .github-inputs {
    color: var(--text-color-dark);
}

.dark-mode .github-input {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
    border-color: #2d2d2d;
}

.create-yaml-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.create-yaml-button:hover {
    background-color: #0e9f71;
}

/* Additional Hacking Theme Elements */
.vibe .site-title::before {
    content: '> ';
    color: var(--accent-color);
    font-family: 'Courier New', monospace;
}

.vibe .prompt-card::before {
    content: '[CLASSIFIED]';
    position: absolute;
    top: -10px;
    right: 10px;
    background: var(--accent-color);
    color: black;
    padding: 2px 6px;
    font-size: 0.6rem;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    transform: rotate(15deg);
    opacity: 0.8;
}

.vibe .search-result-item::before {
    content: '$ ';
    color: var(--accent-color);
    font-family: 'Courier New', monospace;
}

/* Terminal-style scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color-hover);
}

/* Glitch effect for hover states */
@keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

.vibe .prompt-card:hover {
    animation: glitch 0.3s ease-in-out;
}